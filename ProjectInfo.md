# JDL OMS Express 项目信息文档

## 项目概述

**项目名称**: jdl-oms-express  
**项目类型**: 京东物流订单管理系统 - 多垂直业务聚合的分布式微服务架构  
**主要功能**: 提供全渠道物流订单的统一接入、处理、跟踪和管理服务，支持B2C、C2C、O2O等多种业务模式  
**技术架构**: 基于Spring的传统Java Web应用，采用领域驱动设计(DDD)分层架构，支持多垂直业务扩展  
**版本信息**: 1.0.0-SNAPSHOT  

## 目录结构

```
jdl-oms-express/
├── jdl-oms-express-all/                    # 聚合父项目
├── jdl-oms-express-application/            # 应用层 - 业务流程编排
├── jdl-oms-express-client/                 # 客户端SDK
│   ├── jdl-oms-express-client-model/       # 客户端数据模型
│   └── jdl-oms-express-client-service/     # 客户端服务接口
├── jdl-oms-express-domain/                 # 领域层 - 核心业务逻辑
│   ├── jdl-oms-express-domain-adapter/     # 领域适配器
│   ├── jdl-oms-express-domain-extension/   # 领域扩展
│   ├── jdl-oms-express-domain-infrastructure/ # 基础设施
│   ├── jdl-oms-express-domain-model/       # 领域模型
│   ├── jdl-oms-express-domain-service/     # 领域服务
│   └── jdl-oms-express-domain-spec/        # 领域规范
├── jdl-oms-express-horizontal/             # 横向通用能力
├── jdl-oms-express-shared-common/          # 共享通用组件
├── jdl-oms-express-test/                   # 测试模块
├── jdl-oms-express-tracer-agent/           # 链路追踪代理
├── jdl-oms-express-vertical-*/             # 垂直业务模块
│   ├── b2c/                               # B2C业务
│   ├── c2b/                               # C2B业务
│   ├── c2c/                               # C2C业务
│   ├── cc/                                # 冷链业务
│   ├── contract/                          # 合同物流
│   ├── ecp/                               # ECP业务
│   ├── freight/                           # 快运业务
│   ├── fs/                                # 丰速业务
│   ├── intl/                              # 国际业务
│   ├── jxd/                               # 京喜达
│   ├── las/                               # LAS业务
│   ├── lm/                                # 落地配
│   ├── o2o/                               # O2O业务
│   ├── packing/                           # 耗材销售
│   ├── tc/                                # TC业务
│   ├── tms/                               # 运力平台
│   └── uep/                               # UEP业务
├── jdl-oms-express-web/                    # Web层 - 接口暴露
│   ├── jdl-oms-express-main/              # 主Web应用
│   ├── jdl-oms-express-mock/              # Mock服务
│   ├── jdl-oms-express-monitor/           # 监控服务
│   └── jdl-oms-express-worker/            # 工作线程
└── context-engineering/                    # 上下文工程配置
```

## 技术栈信息

### 基础技术栈
- **Java版本**: 1.8
- **Spring版本**: 5.2.9.RELEASE
- **Maven版本**: 3.8.0
- **构建工具**: Maven

### 数据库技术
- **ORM框架**: MyBatis 3.5.6 + MyBatis-Spring 1.3.1
- **数据库**: MySQL 5.1.48
- **连接池**: Apache Commons DBCP2 2.4.0 + Commons Pool2 2.6.2
- **数据库URL**: 
  - 主库: `mysql-cn-north-1-a826a9d9021f4bd9.rds.jdcloud.com:3358/orb`
  - PDQ库: `mysql-cn-north-1-f43a6fe74d8e4ae7.rds.jdcloud.com:3358/jdl-worker`
  - 同步库: `mysql-cn-north-1-debb0377a9324d06.rds.jdcloud.com:3358/cp_order_uat`

### Web技术
- **Web框架**: Spring MVC 5.2.9.RELEASE
- **Servlet版本**: 2.5
- **打包方式**: WAR包
- **模板引擎**: JSP

### 测试技术
- **测试框架**: JUnit 4.13.2
- **Spring测试**: Spring-test 5.2.9.RELEASE
- **Mock框架**: 集成Spring测试支持

### 日志与监控
- **日志框架**: Log4j2 2.18.0-jdsec.rc2
- **监控**: UMP profiler 20240630
- **链路追踪**: 自定义tracer-agent

### 缓存技术
- **一级缓存**: Ehcache
- **二级缓存**: Redis
  - 主机: `redis-kn96n7r71kvu-proxy-nlb.jvessel-open-hb.jdcloud.com:6379``
  - 连接池: maxTotal=600, maxIdle=300

### 消息队列
- **JMQ配置**:
  - 应用名: UATJDLOMSEXPRESS
  - 用户名: UATJDLOMSEXPRESS
  - 地址: jmq-cluster.jd.local:80
  - 降级策略: 自动降级，恢复时间120000ms

### 配置管理
- **DUCC**: 京东统一配置中心 1.4.3
- **UCC**: 京东统一配置客户端 0.0.3-SNAPSHOT
- **配置加密**: Spring-configsec-sdk 1.0.2.RELEASE

### 安全加固
- **Tomcat安全**: jd-security-tomcat 1.11.WEBAPP

## 核心业务服务接口

### 对外提供JSF服务
基于JSF框架提供的服务接口，配置在`properties/ab/jsf-provider.properties`：

#### 主要服务接口
1. **接单服务** (`create`) - 线程池: 400
2. **修改服务** (`modify`) - 线程池: 100
3. **取消服务** (`cancel`) - 线程池: 100
4. **回传服务** (`callback`) - 线程池: 400
5. **删除服务** (`delete`) - 线程池: 100
6. **询价服务** (`enquiry`) - 线程池: 100
7. **拦截服务** (`intercept`) - 线程池: 100
8. **支付服务** (`pay`) - 线程池: 100
9. **重受理服务** (`reaccept`) - 线程池: 100
10. **前置校验服务** (`precheck`) - 线程池: 100
11. **恢复服务** (`recover`) - 线程池: 100

#### JSF配置详情
- **注册中心**: i.jsf.jd.com
- **服务协议**: jsf
- **总线程池**: 1200
- **并发数**: 200

### JMQ消息主题
配置在`properties/ab/jmq.properties`，包含以下主要Topic：

#### 订单生命周期消息
- `cancel_pay_timeOut_order_ab` - 支付超时自动取消
- `callback_order_record_ab` - 订单回传记录
- `modify_order_record_ab` - 订单修改记录
- `cancel_order_record_ab` - 订单取消记录
- `delete_order_record_ab` - 订单删除记录
- `create_order_record_ab` - 订单创建记录
- `recover_order_record_ab` - 订单恢复记录
- `reaccept_order_record_ab` - 订单重受理记录

#### 业务特定消息
- `order_snapshot_msg_ab` - 接单快照记录
- `orderBank_record_ab` - 台账流水记录
- `EXPRESS_ORDER_STATUS_AB` - 订单状态变更通知
- `EXPRESS_ORDER_DATA_AB` - 订单数据流水
- `EXPRESS_ORDER_DATA_UPDATE_NOTICE_AB` - 订单数据变更通知
- `EXPRESS_ORDER_CREATE_NOTICE_AB` - 订单接单成功通知

#### 垂直业务消息
- `init_order_bank_b2c_ab` - B2C台账初始化
- `init_order_bank_c2c_ab` - C2C台账初始化
- `init_order_bank_c2b_ab` - C2B台账初始化
- `coldchain_waybill_unpaid_uat` - 冷链运单待支付
- `eclp_to_lbs_enquiry_fee_ab` - 冷链整车询价计费

## 中间件配置详情

### JSF配置
- **使用模式**: 原生JSF集成
- **依赖版本**: 通过Maven依赖管理
- **配置文件**: 
  - `properties/ab/jsf-provider.properties` - 服务提供者配置
  - `properties/ab/jsf-*-provider.properties` - 各垂直业务专用配置
  - `properties/ab/jsf-consumer.properties` - 服务消费者配置
- **服务注册**: 基于注解和XML配置混合模式
- **线程池管理**: 按服务类型细粒度配置

### JMQ配置
- **使用模式**: 原生JMQ客户端
- **应用标识**: UATJDLOMSEXPRESS
- **配置管理**: 环境变量 + 配置文件
- **消息模式**: 支持集群和广播模式
- **降级策略**: 自动降级，支持配置恢复时间

### Redis配置
- **使用模式**: Jedis客户端 + 连接池
- **部署模式**: 代理模式(Proxy)
- **配置位置**: `properties/ab/redis.properties`
- **Key命名规范**: 
  - 业务前缀 + 订单号 + 操作类型
  - 防重Key: `repeat:{业务}:{订单号}:{操作}`
  - 状态缓存: `status:{业务}:{订单号}`
- **TTL策略**: 按业务场景配置，1分钟到365天不等

### MyBatis配置
- **配置位置**: `mybatis-config.xml`
- **Mapper位置**: `mapper/`目录下按业务分类
- **SQL文件**: XML格式，支持动态SQL
- **缓存配置**: 集成Ehcache二级缓存

### Spring配置
- **配置方式**: XML + Properties混合配置
- **配置文件**:
  - `applicationContext.xml` - 主配置
  - `spring/`目录下按功能模块细分
  - `properties/`目录下按环境分离

## 架构实现细节

### DDD分层实现
```
Web层 (jdl-oms-express-web)
├── Controller/REST接口
├── JSP页面
└── 配置管理

应用层 (jdl-oms-express-application)
├── 业务流程编排
├── 应用服务
└── 用例实现

领域层 (jdl-oms-express-domain)
├── 领域模型 (domain-model)
├── 领域服务 (domain-service)
├── 领域规范 (domain-spec)
├── 基础设施 (domain-infrastructure)
├── 适配器 (domain-adapter)
└── 扩展点 (domain-extension)

客户端层 (jdl-oms-express-client)
├── 客户端模型
└── 客户端服务

横向通用 (jdl-oms-express-horizontal)
├── 通用扩展
├── 基础设施
└── 通用模型

垂直业务 (jdl-oms-express-vertical-*)
├── 业务扩展
└── 业务基础设施
```

### 扩展点机制
- **扩展接口**: 基于领域驱动设计的扩展点模式
- **实现方式**: 每个垂直业务独立模块，通过SPI机制加载
- **注册机制**: Spring自动扫描 + 配置驱动
- **业务隔离**: 各垂直业务代码完全隔离，通过接口交互

### 配置管理模式
- **环境分离**: properties目录下按环境(ab/pre/test/online/yc)分离
- **配置优先级**: 环境变量 > 系统属性 > 配置文件
- **动态配置**: 支持DUCC动态配置更新
- **配置加密**: 敏感信息支持加密存储

## 代码约定

### 包结构约定
```
cn.jdl.oms.express
├── main/                    # Web层启动类
├── vertical.{业务}/         # 垂直业务实现
├── horizontal/              # 横向通用能力
├── domain/                  # 领域层
├── client/                  # 客户端
└── shared/                  # 共享组件
```

### 命名约定
- **类名**: 大驼峰命名，功能清晰
- **方法名**: 小驼峰命名，动词开头
- **变量名**: 小驼峰命名，语义明确
- **常量名**: 全大写，下划线分隔
- **配置文件**: 小写+连字符，环境后缀

### 注解使用约定
- **Spring注解**: @Service, @Repository, @Component
- **JSF注解**: 基于XML配置为主
- **MyBatis注解**: XML映射文件为主
- **验证注解**: JSR-303验证框架

## 集成约束

### 技术栈约束
- **Java版本**: 必须使用1.8
- **Spring版本**: 必须使用5.2.9.RELEASE
- **MyBatis版本**: 必须使用3.5.6
- **MySQL驱动**: 必须使用5.1.48

### 中间件约束
- **JSF配置**: 必须按服务类型配置独立线程池
- **JMQ配置**: 必须按业务场景配置独立Topic
- **Redis配置**: 必须按业务场景配置TTL
- **数据库配置**: 必须按读写分离配置

### 文件路径约束
- **Controller**: `src/main/java/cn/jdl/oms/express/main/controller/`
- **Service**: 按垂直业务分包
- **Mapper**: `src/main/resources/mapper/{业务}/`
- **配置**: `src/main/resources/properties/{环境}/`

### 配置约束
- **新增配置**: 优先在现有配置文件中添加
- **环境配置**: 必须同步更新所有环境的配置文件
- **敏感信息**: 必须使用配置加密
- **动态配置**: 优先使用DUCC配置

## 业务模式约束
- 本系统全部逻辑为接收外部请求，内部处理逻辑，调用外部接口！不需要连接数据库建库建表
实现功能！！！

## 环境变量清单

### JSF相关
- `JSF_REGISTRY_INDEX`: 注册中心地址 (默认: i.jsf.jd.com)
- `JSF_ENABLED`: JSF开关
- `JSF_SERVER_PORT`: 服务端端口
- `JSF_TOKEN`: 安全令牌

### JMQ相关
- `JMQ_ADDRESS`: 消息服务器地址
- `JMQ_PASSWORD`: 连接密码
- `JMQ_PRODUCER_APP`: 生产者应用名

### Redis相关
- `REDIS_HOST`: Redis主机
- `REDIS_PORT`: Redis端口
- `REDIS_PASSWORD`: Redis密码

### 数据库相关
- `JDBC_URL`: 数据库连接URL
- `JDBC_USERNAME`: 数据库用户名
- `JDBC_PASSWORD`: 数据库密码

## 开发约束和规范

### 新功能开发约束
1. **垂直业务开发**: 必须在对应vertical模块下开发
2. **通用能力开发**: 必须在horizontal或shared-common模块
3. **领域模型**: 必须符合DDD规范，放在domain层
4. **接口定义**: 必须先定义在client模块

### 外部集成约束
1. **JSF服务**: 必须配置独立线程池
2. **JMQ消息**: 必须配置独立Topic
3. **数据库**: 必须配置读写分离
4. **缓存**: 必须配置TTL策略

### 测试要求
1. **单元测试**: 每个Service必须有单元测试
2. **集成测试**: 每个JSF接口必须有集成测试
3. **配置测试**: 每个环境配置必须验证

### 代码审查要求
1. **命名规范**: 必须符合命名约定
2. **配置管理**: 必须同步所有环境配置
3. **安全审查**: 敏感信息必须加密
4. **性能审查**: 必须评估线程池配置

## 配置文件清单

### 应用配置
- `applicationContext.xml` - Spring主配置
- `web.xml` - Web应用配置
- `mybatis-config.xml` - MyBatis配置
- `log4j2.xml` - 日志配置

### 中间件配置
- `properties/ab/jsf-provider.properties` - JSF服务配置
- `properties/ab/jmq.properties` - JMQ消息配置
- `properties/ab/redis.properties` - Redis缓存配置
- `properties/ab/jdbc.properties` - 数据库配置

### 环境配置
- `properties/ab/` - AB测试环境
- `properties/pre/` - 预发环境
- `properties/test/` - 测试环境
- `properties/online/` - 线上环境
- `properties/yc/` - 压测环境

## 生成信息

- **文档生成时间**: 2025-09-05 17:00:00
- **项目版本**: 1.0.0-SNAPSHOT
- **分析工具**: Init-Project工作流完整指令
- **文档状态**: 完整项目信息文档
- **维护建议**: 建议每次重大版本更新时同步更新此文档