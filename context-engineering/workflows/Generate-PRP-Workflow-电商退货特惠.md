# Generate-PRP工作流完整指令 - 电商退货特惠功能

## 工作流概述
基于joy-context-engineering上下文工程最佳实践，为电商退货特惠功能生成完整的PRP（Pull Request Proposal）工作流指令。

## 前置条件检查清单

### 1. 需求文档验证
- [x] 已读取本地需求文档：`电商特惠prd.md`
- [x] 已读取项目信息文档：`ProjectInfo.md`
- [x] 已验证现有PRP文件：`电商退货特惠-PRP-20250911.md`

### 2. 环境准备
- [x] 确认项目结构：jdl-oms-express多垂直业务聚合架构
- [x] 确认技术栈：Java 8 + Spring 5.2.9 + MyBatis + MySQL
- [x] 确认DDD分层架构：domain/application/infrastructure/client/web

## Generate-PRP工作流步骤

### 步骤1: 需求解析与上下文构建
```bash
# 执行命令
joy-context-engineering analyze-requirements \
  --prd-file="/Users/<USER>/IdeaProjects/jdl-oms-express/电商特惠prd.md" \
  --project-info="/Users/<USER>/IdeaProjects/jdl-oms-express/ProjectInfo.md" \
  --output-context="context-engineering/temp/requirements-context.json"
```

### 步骤2: 架构影响分析
```bash
# 执行架构影响评估
joy-context-engineering analyze-architecture-impact \
  --feature="电商退货特惠" \
  --product-code="ed-m-0076" \
  --affected-modules="domain-spec,domain-infrastructure,c2c-extension,shared-common,client-model" \
  --context-file="context-engineering/temp/requirements-context.json"
```

### 步骤3: 代码模式识别
```bash
# 识别相关代码模式
joy-context-engineering identify-code-patterns \
  --pattern-types="product-enum,modify-validation,payment-restriction,data-mapping,message-extension" \
  --search-paths="jdl-oms-express-*/src/main/java" \
  --output-patterns="context-engineering/temp/code-patterns.json"
```

### 步骤4: 生成PRP模板
```bash
# 基于最佳实践生成PRP
joy-context-engineering generate-prp \
  --template="context-engineering/PRPs/templates/java-ddd-prp-template.md" \
  --feature-name="电商退货特惠" \
  --requirements="context-engineering/temp/requirements-context.json" \
  --patterns="context-engineering/temp/code-patterns.json" \
  --output="context-engineering/PRPs/电商退货特惠-PRP-20250911.md"
```

### 步骤5: 验证PRP完整性
```bash
# 验证PRP文件
joy-context-engineering validate-prp \
  --prp-file="context-engineering/PRPs/电商退货特惠-PRP-20250911.md" \
  --checklist="completeness,consistency,traceability" \
  --project-standards="context-engineering/platform-specific/"
```

## 具体实现指令

### 1. 产品枚举扩展实现
```java
// 目标文件: jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/ProductEnum.java
// 添加新产品枚举
ECOMMERCE_RETURN("ed-m-0076", "电商退货", 31, "N")
```

### 2. 地址修改限制实现
```java
// 目标文件: jdl-oms-express-vertical-c2c/src/main/java/cn/jdl/oms/express/vertical/c2c/extension/white/C2CModifyWhiteExtension.java
// 实现地址修改验证逻辑
@Override
public void validateAddressModification(ExpressOrderContext context) {
    ExpressOrderModel order = context.getOrderModel();
    ChangedPropertyDelegate changed = context.getChangedPropertyDelegate();
    
    if (isEcommerceReturnProduct(order) && isBeforePickup(order)) {
        if (changed.addressLevel123Changed()) {
            throw new BusinessDomainException("ADDRESS_MODIFICATION_RESTRICTED")
                .withCustom("电商退货产品揽收前不允许修改省市区地址");
        }
    }
}
```

### 3. 支付方式限制实现
```java
// 目标文件: jdl-oms-express-shared-common/src/main/java/cn/jdl/oms/express/shared/common/dict/ECardDisableReasonEnum.java
// 添加支付限制枚举
ECOMMERCE_RETURN_ECARD_DISABLE("ECOMMERCE_RETURN", "电商退货产品不支持E卡支付")

// 在支付校验逻辑中添加
private void validatePaymentMethod(ExpressOrderModel order) {
    if (ProductEnum.ECOMMERCE_RETURN.getCode().equals(order.getMajorProductNo())) {
        if (hasECardPayment(order)) {
            throw new BusinessDomainException("ECARD_NOT_SUPPORTED")
                .withCustom("电商退货产品不支持E卡支付");
        }
    }
}
```

### 4. 数据同步映射实现
```java
// 目标文件: jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/message/pl/waybill/WaybillInfoMappingUtil.java
// 更新waybillSign映射
public void updateWaybillSignForEcommerceReturn(MainProduct mainProduct, String markUtil) {
    if (ProductEnum.ECOMMERCE_RETURN.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
        mainProduct.setWaybillSign("0"); // 不支持E卡支付
        mainProduct.setProductName(ProductEnum.ECOMMERCE_RETURN.getDesc());
        mainProduct.setProductNo(ProductEnum.ECOMMERCE_RETURN.getCode());
    }
}
```

### 5. 消息字段扩展实现
```java
// 目标文件: jdl-oms-express-client-model/src/main/java/cn/jdl/oms/express/model/CreateExpressOrderRequest.java
// 添加附件信息字段
private List<AttachmentInfo> attachmentInfos;

// 在消息转换器中处理
public ExpressOrderMessage convertToMessage(CreateExpressOrderRequest request) {
    ExpressOrderMessage message = new ExpressOrderMessage();
    // ... 其他字段转换 ...
    
    if (request.getAttachmentInfos() != null) {
        message.setAttachmentInfos(
            request.getAttachmentInfos().stream()
                .map(this::convertAttachmentInfo)
                .collect(Collectors.toList())
        );
    }
    
    return message;
}
```

## 测试用例生成指令

### 单元测试模板
```java
// 测试文件: jdl-oms-express-test/src/test/java/cn/jdl/oms/express/vertical/c2c/EcommerceReturnTest.java
@Test
public void testEcommerceReturnProductValidation() {
    // 测试产品枚举正确性
    ProductEnum ecommerceReturn = ProductEnum.ECOMMERCE_RETURN;
    assertEquals("ed-m-0076", ecommerceReturn.getCode());
    assertEquals("N", ecommerceReturn.getSign());
}

@Test
public void testAddressModificationRestriction() {
    // 测试揽收前地址修改限制
    ExpressOrderModel order = createEcommerceReturnOrder();
    order.setOrderStatus(OrderStatus.WAIT_ACCEPT);
    
    ChangedPropertyDelegate changed = new ChangedPropertyDelegate();
    changed.addChangedProperty(ModifyItemConfigEnum.ADDRESS_LEVEL1, "old", "new");
    
    assertThrows(BusinessDomainException.class, () -> {
        c2cModifyWhiteExtension.validateAddressModification(order, changed);
    });
}
```

## 代码审查检查单

### 功能性检查
- [ ] 新产品枚举定义正确
- [ ] 地址修改限制逻辑完整
- [ ] 支付限制实现正确
- [ ] 数据同步映射准确
- [ ] 消息字段扩展完整

### 代码质量检查
- [ ] 遵循现有代码风格
- [ ] 添加适当的注释
- [ ] 异常处理完善
- [ ] 日志记录完整
- [ ] 单元测试覆盖

### 性能检查
- [ ] 无性能瓶颈
- [ ] 内存使用合理
- [ ] 并发安全考虑

## 发布验证步骤

### 预发布验证
1. **功能验证**: 手动测试所有功能点
2. **数据验证**: 验证数据正确性
3. **性能验证**: 检查响应时间
4. **兼容性验证**: 确保不影响现有功能

### 生产发布
1. **灰度发布**: 10%流量验证
2. **监控观察**: 观察关键指标
3. **全量发布**: 验证通过后全量
4. **回滚准备**: 保留快速回滚能力

## 文档更新清单

### 技术文档
- [ ] API文档更新
- [ ] 数据库文档更新
- [ ] 部署文档更新
- [ ] 运维手册更新

### 业务文档
- [ ] 产品说明更新
- [ ] 用户手册更新
- [ ] 培训材料更新

## 工作流执行状态跟踪

```json
{
  "workflow_status": {
    "requirements_analysis": "completed",
    "architecture_impact": "completed", 
    "code_pattern_identification": "completed",
    "prp_generation": "completed",
    "prp_validation": "pending",
    "implementation": "pending",
    "testing": "pending",
    "deployment": "pending"
  },
  "current_phase": "prp_validation",
  "next_action": "执行PRP验证命令"
}
```

## 注意事项

1. **约束遵守**: 严格按照"不可以生成新的扩展点执行逻辑"的要求
2. **代码风格**: 遵循项目现有Java代码规范
3. **异常处理**: 使用BusinessDomainException进行业务异常处理
4. **日志规范**: 使用SLF4J日志框架，适当级别记录
5. **测试要求**: 单元测试覆盖率>80%，集成测试覆盖主要场景

## 支持工具

### 代码生成工具
```bash
# 使用joy-context-engineering工具生成骨架代码
joy-context-engineering generate-code \
  --prp="context-engineering/PRPs/电商退货特惠-PRP-20250911.md" \
  --output="src/main/java" \
  --templates="context-engineering/templates/java-ddd"
```

### 测试生成工具
```bash
# 生成测试用例
joy-context-engineering generate-tests \
  --prp="context-engineering/PRPs/电商退货特惠-PRP-20250911.md" \
  --output="src/test/java" \
  --framework="junit4"
```

此Generate-PRP工作流完整指令基于joy-context-engineering最佳实践，结合电商退货特惠功能的具体需求，提供了从需求分析到生产发布的全流程指导。