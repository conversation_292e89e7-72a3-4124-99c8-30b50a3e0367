# PRP (Pull Request Proposal) - 电商退货特惠功能实现

## 基本信息
- **功能名称**: 电商退货特惠
- **产品代码**: ed-m-0076
- **PRP编号**: PRP-20250911-001
- **创建日期**: 2025-09-11
- **需求文档**: 电商特惠prd.md
- **项目**: jdl-oms-express

## 需求概述
为OMS系统添加电商退货（ed-m-0076）主产品支持，包括：
1. 主产品数据同步与枚举定义
2. 地址修改限制规则（C2C业务揽收前限制）
3. E卡支付限制
4. 接单消息字段扩展（attachmentInfos）

## 技术方案

### 1. 产品枚举扩展
**目标文件**: `jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/ProductEnum.java`

**变更内容**:
```java
// 添加新产品枚举
ECOMMERCE_RETURN("ed-m-0076", "电商退货", 31, "N")
```

### 2. 地址修改限制实现
**目标文件**: `jdl-oms-express-vertical-c2c/src/main/java/cn/jdl/oms/express/vertical/c2c/extension/white/C2CModifyWhiteExtension.java`

**核心逻辑**:
```java
private void validateAddressModificationForEcommerceReturn(ExpressOrderModel order, ChangedPropertyDelegate changed) {
    if (ProductEnum.ECOMMERCE_RETURN.getCode().equals(order.getMajorProductNo())) {
        if (isBeforePickup(order) && changed.addressLevel123Changed()) {
            throw new BusinessDomainException("ADDRESS_MODIFICATION_RESTRICTED")
                .withCustom("电商退货产品揽收前不允许修改省市区地址");
        }
    }
}
```

### 3. 支付方式限制
**目标文件**: `jdl-oms-express-shared-common/src/main/java/cn/jdl/oms/express/shared/common/dict/ECardDisableReasonEnum.java`

**变更内容**:
```java
// 添加支付限制枚举
ECOMMERCE_RETURN_ECARD_DISABLE("ECOMMERCE_RETURN", "电商退货产品不支持E卡支付")
```

**实现位置**: 各OrderBankFacadeTranslator类中的支付校验逻辑

### 4. 数据同步映射
**目标文件**: `jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/message/pl/waybill/WaybillInfoMappingUtil.java`

**变更内容**:
```java
public void processEcommerceReturnProduct(MainProduct mainProduct, String markUtil) {
    if (ProductEnum.ECOMMERCE_RETURN.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
        mainProduct.setWaybillSign("0"); // 第一位设为0，不支持E卡
        mainProduct.setProductName(ProductEnum.ECOMMERCE_RETURN.getDesc());
        mainProduct.setProductNo(ProductEnum.ECOMMERCE_RETURN.getCode());
    }
}
```

### 5. 消息字段扩展
**目标文件**: `jdl-oms-express-client-model/src/main/java/cn/jdl/oms/express/model/CreateExpressOrderRequest.java`

**变更内容**:
```java
private List<AttachmentInfo> attachmentInfos;

// getter/setter方法
public List<AttachmentInfo> getAttachmentInfos() {
    return attachmentInfos;
}

public void setAttachmentInfos(List<AttachmentInfo> attachmentInfos) {
    this.attachmentInfos = attachmentInfos;
}
```

## 影响范围分析

### 模块影响
| 模块 | 影响程度 | 变更类型 |
|------|----------|----------|
| jdl-oms-express-domain-spec | 低 | 枚举扩展 |
| jdl-oms-express-domain-infrastructure | 中 | 数据映射逻辑 |
| jdl-oms-express-vertical-c2c | 高 | 业务规则实现 |
| jdl-oms-express-shared-common | 中 | 支付限制枚举 |
| jdl-oms-express-client-model | 低 | 消息字段扩展 |

### 接口影响
- **JSF服务接口**: 无变更
- **消息格式**: 新增attachmentInfos字段（向后兼容）
- **数据库结构**: 无变更

## 测试策略

### 单元测试
**测试文件**: `jdl-oms-express-test/src/test/java/cn/jdl/oms/express/vertical/c2c/EcommerceReturnTest.java`

```java
@Test
public void testEcommerceReturnProductEnum() {
    ProductEnum product = ProductEnum.ECOMMERCE_RETURN;
    assertEquals("ed-m-0076", product.getCode());
    assertEquals("N", product.getSign());
    assertEquals(31, product.getSignIndex());
}

@Test
public void testAddressModificationRestrictionBeforePickup() {
    // 测试揽收前地址修改限制
    ExpressOrderModel order = createTestOrder(ProductEnum.ECOMMERCE_RETURN);
    order.setOrderStatus(OrderStatus.WAIT_ACCEPT);
    
    ChangedPropertyDelegate changed = new ChangedPropertyDelegate();
    changed.addChangedProperty(ModifyItemConfigEnum.ADDRESS_LEVEL1, "110000", "310000");
    
    assertThrows(BusinessDomainException.class, () -> {
        modifyWhiteExtension.validateModification(order, changed);
    });
}

@Test
public void testECardPaymentRestriction() {
    // 测试E卡支付限制
    ExpressOrderModel order = createTestOrder(ProductEnum.ECOMMERCE_RETURN);
    order.setPaymentMethod(PaymentMethod.E_CARD);
    
    assertThrows(BusinessDomainException.class, () -> {
        paymentValidator.validate(order);
    });
}
```

### 集成测试
1. **端到端测试**: 创建电商退货订单完整流程
2. **边界测试**: 揽收前后地址修改权限验证
3. **兼容性测试**: 旧版本客户端消息处理

## 部署计划

### 阶段1: 代码开发
- [ ] 产品枚举扩展
- [ ] 地址修改限制实现
- [ ] 支付限制实现
- [ ] 数据同步映射
- [ ] 消息字段扩展

### 阶段2: 测试验证
- [ ] 单元测试编写与执行
- [ ] 集成测试执行
- [ ] 回归测试执行

### 阶段3: 部署上线
- [ ] 预发布环境验证
- [ ] 生产环境部署
- [ ] 监控验证

## 风险评估

### 低风险项
- 产品枚举扩展：标准枚举添加，无风险
- 消息字段扩展：向后兼容，无风险

### 中风险项
- 地址修改限制：需确保业务规则正确性
- 支付限制：需确保不影响其他支付方式

### 缓解措施
1. 充分的单元测试覆盖
2. 灰度发布策略
3. 实时监控和回滚机制

## 回滚方案
1. **代码回滚**: 回滚到上一个稳定版本
2. **配置回滚**: 如有必要，通过DUCC配置中心回滚功能开关
3. **数据修复**: 如有数据问题，提供数据修复脚本

## 监控指标
- 电商退货订单创建成功率
- 地址修改拒绝率
- E卡支付拒绝率
- 消息处理成功率
- 系统整体性能指标

## 相关文档
- [需求文档](电商特惠prd.md)
- [项目信息](ProjectInfo.md)
- [代码模式文档](context-engineering/examples/code-patterns/)
- [测试用例](jdl-oms-express-test/src/test/java/cn/jdl/oms/express/vertical/c2c/EcommerceReturnTest.java)

## 审批记录
- [ ] 技术负责人审批
- [ ] 产品经理审批
- [ ] 测试负责人审批
- [ ] 运维负责人审批

---
**生成时间**: 2025-09-11 23:00:00  
**生成工具**: joy-context-engineering Generate-PRP工作流  
**版本**: v1.0.0