# 项目MQ消息对应关系表

## 概述
本文档整理了jdl-oms-express项目中所有对外发送的MQ消息及其对应的消息类和转换类。

## 对应关系表

### 1. 订单生命周期消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| `EXPRESS_ORDER_CREATE_NOTICE` | `CreateOrderNotice` | `CreateOrderNoticeTranslator` | 订单接单成功通知 |
| `EXPRESS_ORDER_STATUS` | `OrderStatusNotifyMessageDto` | `OrderStatusNotifyDataDtoTranslator` | 订单状态变更通知 |
| `EXPRESS_ORDER_DATA` | `OrderDataFlowDto` | 无 | 订单数据流水记录 |
| `EXPRESS_ORDER_DATA_UPDATE_NOTICE` | `OrderDataUpdateDto` | 无 | 订单数据变更通知 |

### 2. 支付相关消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| `cancel_pay_timeout_order` | `PayTimeoutCancelMessageDto` | 无 | 支付超时自动取消 |
| `pay_return_info` | `PayReturnInfoMessageDto` | 无 | 科技支付结果 |

### 3. 台账和快照消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| `orderBank_record` | `OrderBankFlowDto` | 无 | 台账流水记录 |
| `order_snapshot` | 使用通用消息格式 | 无 | 接单快照记录 |

### 4. 业务特定消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| `eclp_to_lbs_enquiry_fee` | `PushLBSFeeMessageDto` | 无 | 冷链整车询价推计费 |
| `b_normal_specail_fee` | `BNormalSpecailFeeJmqMessageDto` | 无 | B网特殊费用 |
| `EXPRESS_ORDER_INVOICE` | 使用通用消息格式 | 无 | 纯配通用发票推送 |
| `EXPRESS_ORDER_COMMON_ASYNC_ENQUIRY` | `CommonAsyncEnquiryJmqMessageDto` | `CommonAsyncEnquiryJmqTranslator` | 纯配异步询价消息 |

### 5. TMS相关消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| `tms_enquiry_cancel_notify` | `TmsEnquiryCancelNotifyMessageDto` | 无 | 询价系统推送取消消息 |
| `tms_enquiry_confirm_back` | `TmsEnquiryConfirmBackMessageDto` | 无 | 询价回传消息 |
| `ldop_middle_enquiry_bill_back` | 使用通用消息格式 | 无 | 快运TMS询价回传 |
| `ldop_middle_enquiry_vehicle_driver` | `EnquiryVehicleDriverJmqMessageDto` | 无 | 询价车辆信息回传 |

### 6. 冷链相关消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| `lbs2eclp_fee_infos_result` | `CCFeeInfoResultJmqMessageDto` | 无 | 冷链计费结果 |
| `coldchain_waybill_unpaid` | `ColdChainWaybillUnpaidMessageDto` | 无 | 冷链运单待支付运单 |

### 7. 其他业务消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| `ldop_package_number` | `LdopPackageNumberMessageDto` | 无 | LDOP包裹数量变更 |
| `DELIVERY_PICKUP_SYNC_BIND` | `DeliveryPickupSyncBindMessageDto` | 无 | 取件单接单成功绑定派送单 |
| `PUSH_EBS_INFO_FREIGHT` | `PushEBSJmqMessageDto` | 无 | EBS信息推送 |

### 8. CRM相关消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| 快运整车直达报价通知CRM | `EnquiryNoticeCRMMessageDto` | 无 | 快运整车直达报价通知CRM |

### 9. 订单记录消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| `create_order_record` | 使用通用消息格式 | 无 | 订单创建记录 |
| `modify_order_record` | 使用通用消息格式 | 无 | 订单修改记录 |
| `cancel_order_record` | `CancelOrderMessageDto` | 无 | 订单取消记录 |
| `delete_order_record` | `DeleteOrderDto` | 无 | 订单删除记录 |
| `recover_order_record` | 使用通用消息格式 | 无 | 订单恢复记录 |
| `reaccept_order_record` | 使用通用消息格式 | 无 | 订单重受理记录 |

### 10. 台账初始化消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| `init_order_bank_c2c` | `OrderBankInitJmqMessageDto` | 无 | C2C台账初始化 |
| `init_order_bank_b2c` | `OrderBankInitJmqMessageDto` | 无 | B2C台账初始化 |
| `init_order_bank_c2b` | `OrderBankInitJmqMessageDto` | 无 | C2B台账初始化 |

## 消息类命名规范

根据分析，项目中的消息类遵循以下命名规范：

1. **EXPRESS_ORDER_CREATE_NOTICE** → `CreateOrderNotice`
2. **其他消息** → 通常以 `MessageDto` 或 `JmqMessageDto` 结尾

## 转换类命名规范

转换类通常遵循以下规范：
1. 消息类名 + `Translator`
2. 例如：`CreateOrderNoticeTranslator`、`CommonAsyncEnquiryJmqTranslator`

## 注意事项

1. 部分消息使用通用消息格式，没有专门的消息类
2. 部分消息类没有对应的转换类，直接在业务代码中构建
3. 所有转换类都使用 `@Translator` 注解标注
4. 消息类都继承自 `CommonDto` 基类
