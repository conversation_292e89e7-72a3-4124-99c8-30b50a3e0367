package cn.jdl.oms.express.domain.spec.dict;


import java.util.EnumSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 订单中心所有主产品枚举集合
 */
public enum ProductEnum {
    THS("ed-m-0001", "京东标快",31,"0"),
    TKS("ed-m-0002", "京东特快",31,"1"),
    TSSTC("ed-m-0005", "同城急送",31,"7"),
    TSSCJ("ed-m-0006", "特瞬送城际",31,"3"),
    TCSP("ed-m-tcsp", "同城速配",31,"8"),
    SXTK("LL-SD-M", "生鲜特快",31,"9"),
    SXTH("LL-HD-M", "生鲜标快",31,"A"),
    KDWXJ("ed-m-0010", "快递微小件",31,"5"),
    THBG("ed-m-0012", "特惠包裹",31,"C"),
    HSD("ed-m-0017", "函速达",31,"B"),
    QCS("ed-m-0016", "全城送",31,"E"),
    TKBG("ed-m-0020", "特快包裹",31,"H"),
    SXZS("LL-ZS-M", "生鲜专送",55,"1"),
    GXS("ed-m-0018","共享送",31,"K"),
    THXJ("ed-m-0019","京东特惠",31,"F"),
    DSTH("ed-m-0059","电商标快",31,"M"),
    JDBS("ed-m-0072","京东帮送",null,null),
    KCJS("ed-m-0075","跨城急送",null,null),
    ECOMMERCE_RETURN("ed-m-0076","电商退货",31,"N"),
    /**
     * 纯配同城产品编码
     */
    SAME_CITY_EXPRESS_PRODUCT_NO("lq-m-0012","纯配同城产品编码", -1, ""),

    /**
     * 物流平台-百万商家
     */
    YTO_EXPRESS("sc-m-0071", "卓配-圆通", null, null),
    ZTO_EXPRESS("sc-m-0072", "卓配-中通", null, null),
    YUN_DA_EXPRESS("sc-m-0073", "卓配-韵达", null, null),
    STO_EXPRESS("sc-m-0075", "卓配-申通", null, null),
    JT_EXPRESS("sc-m-0076", "卓配-极兔", null, null),
    DEPPON_EXPRESS("sc-m-0077", "卓配-德邦", null, null),

    /**
     * 物流平台-pop售后
     */
    JDL_FREIGHT("fr-m-0013", "京东快运", null, null),
    DP_EXPRESS("fr-m-0014", "德邦快递", null, null),
    DP_FREIGHT("fr-m-0015", "德邦快运", null, null),
    THS_POP("ed-m-0070", "特惠送-POP", null, null),
    JI_TU("ed-m-0071", "极兔特惠送-POP售后", null, null),

    CC_YYZS("md-m-0005","医药专送", -1, ""),
    CC_LLZS("ll-m-0015","冷链专送", -1, ""),
    CC_YYLL("ll-m-0002","医药冷链", -1, ""),
    TKLD("fr-m-0001","特快零担",80,"2"),
    THZH("fr-m-0002","特惠重货",80,"1"),
    TKZH("fr-m-0004","特快重货",80,"9"),
    KYLD("fr-m-0006","快运零担",80,"0"),
    HKZH("fr-m-0007","航空重货", 80, "C"),
    THPH("fr-m-0017","特惠专配",80,"D"),
    FTL_DIRECT("fr-m-0003","整车直达",null,null),
    ACSD("fr-m-0018","爱宠速递",80,"E"),
    /**
     * 冷链产品编码
     */
    LLKB("LL-KB-M", "冷链卡班", null, null),
    LLXP("ll-m-0020", "冷链小票", null, null),
    LLCP("LL-CP-M", "冷链城配", null, null),
    LLZC("LL-ZC-M", "冷链整车", null, null),
    /**
     * 医药产品编码
     */
    YYLD("LL-YYLD-M", "医药零担", null, null),
    YYDP("ll-m-0018", "医药大票", null, null),
    YLLD("md-m-0002", "医冷零担", null, null),
    YYZC("LL-YYZC-M", "医药整车", null, null),
    /**
     * 合同物流品编码
     */
    CONTRACT_ZC("fr-m-0009","B2B整车",null,""),
    CONTRACT_LD("fr-m-0010","B2B零担",null,""),
    CONTRACT_DPZS("tc-m-0006","大票直送",null,""),
    CONTRACT_ZCZS("tc-m-0007","整车专送",null,""),
    CONTRACT_SUPPLY("sc-m-0065","JD工业企配业务增值产品",null,""),

    /**
     * 专线包仓
     */
    ZHUAN_XIAN_BAO_CANG("tn-m-0003", "专线包仓", null, null),

    BHC("is-m-0004", "补货仓城运", null, null),

    ;

    private String code;
    private String desc;
    /*外单打标位*/
    private Integer index;
    /*外单打标位对应的值*/
    private String sign;

    ProductEnum(String code, String desc,Integer index,String sign) {
        this.code = code;
        this.desc = desc;
        this.index = index;
        this.sign = sign;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 外单打标位
     * @return
     */
    public Integer getIndex() {
        return index;
    }

    /**
     * 外单打标位对应的值
     * @return
     */
    public String getSign() {
        return sign;
    }

    public static ProductEnum of(String code) {
        return registry.get(code);
    }

    public static boolean isFreshMainProduct(String code){
        //与胡智阳确认生鲜专送不算生鲜产品
        return SXTK.code.equals(code) || SXTH.code.equals(code);
    }

    private static final Map<String, ProductEnum> registry = new HashMap();

    static {
        Iterator iterator = EnumSet.allOf(ProductEnum.class).iterator();
        while (iterator.hasNext()) {
            ProductEnum typeEnum = (ProductEnum) iterator.next();
            registry.put(typeEnum.getCode(), typeEnum);
        }
    }
}
