package cn.jdl.oms.express.domain.spec.dict;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public enum ModifyRecordTypeEnum {
    READDRESS("1001", "改址记录"),
    RECHECK("1002", "复重量方询价记录"),
    INTERCEPT("1003", "拦截记录"),
    REJECT("1004", "拒收记录"),
    ;

    private static final Map<String, ModifyRecordTypeEnum> registry = new HashMap();
    private String code;
    private String desc;

    ModifyRecordTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ModifyRecordTypeEnum of(String code) {
        return registry.get(code);
    }

    static {
        Iterator iterator = EnumSet.allOf(ModifyRecordTypeEnum.class).iterator();
        while (iterator.hasNext()) {
            ModifyRecordTypeEnum typeEnum = (ModifyRecordTypeEnum) iterator.next();
            registry.put(typeEnum.getCode(), typeEnum);
        }
    }
}
