package cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill;

import cn.jdl.oms.express.domain.dto.ActivityInfoDto;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.dto.AgreementInfoDto;
import cn.jdl.oms.express.domain.dto.AttachmentInfoDto;
import cn.jdl.oms.express.domain.dto.BusinessIdentityDto;
import cn.jdl.oms.express.domain.dto.CargoInfoDto;
import cn.jdl.oms.express.domain.dto.ChannelInfoDto;
import cn.jdl.oms.express.domain.dto.ConsigneeInfoDto;
import cn.jdl.oms.express.domain.dto.ConsignorInfoDto;
import cn.jdl.oms.express.domain.dto.CostInfoDto;
import cn.jdl.oms.express.domain.dto.CustomerInfoDto;
import cn.jdl.oms.express.domain.dto.DimensionInfoDto;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.FulfillmentInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.PatternInfoDto;
import cn.jdl.oms.express.domain.dto.PointsInfoDto;
import cn.jdl.oms.express.domain.dto.ProductInfoDto;
import cn.jdl.oms.express.domain.dto.PromotionInfoDto;
import cn.jdl.oms.express.domain.dto.QuantityInfoDto;
import cn.jdl.oms.express.domain.dto.ShipmentInfoDto;
import cn.jdl.oms.express.domain.dto.TicketInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.waybill.WayBillDetailResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.waybill.WayBillQueryResponse;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachFeeEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.CargoVulnerableEnum;
import cn.jdl.oms.express.domain.spec.dict.ContactlessTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.FeeDetailMainEnum;
import cn.jdl.oms.express.domain.spec.dict.HiddenContentEnum;
import cn.jdl.oms.express.domain.spec.dict.LengthTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.MedicalWarmLayerEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceRequirementsEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.dict.TransportTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.VolumeTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.WarmLayerEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightTypeEnum;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.CargoDelegate;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.BusinessConstants;
import cn.jdl.oms.express.shared.common.constant.MagicCommonConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.B2CExtendStatusEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressDeliveryOperationModeEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressDeliveryOperationModeType;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.TypeConversion;
import com.alibaba.fastjson.JSONArray;
import com.jd.ldop.center.api.receive.dto.ValueAddedServiceDTO;
import com.jd.ldop.center.api.waybill.dto.ExtendMessageDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ProjectName：com.jdl.cp.oms.doo.domain.infs.ohs.message.pl
 * @Package： com.jdl.cp.oms.doo.domain.infs.ohs.locals.message.pl.waybill
 * @ClassName: WaybillInfoMappingUtil
 * @Description: 运单信息映射工具类
 * @Author： yangxiangrong5
 * @CreateDate 2021/04/12  8:33 下午
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Component
public class WaybillInfoMappingUtil {
    /** Logger */
    private static final Logger LOGGER = LoggerFactory.getLogger(WaybillInfoMappingUtil.class);
    /** 外单保温箱数量key */
    private static final String WAYBILL_BOX_NUM_KEY = "incubatorNum";
    /** 产品中心保温箱数量key */
    private static final String PRODUCT_BOX_NUM_KEY = "incubatorQty";
    /** 免赔协议 */
    private static final String DEDUCTIBLE_SERVICE = "deductibleService";

    /** 指定签收 */
    private static final String COMBINE_SIGN = "combineSign";

    /** 签收类型 */
    private static final String ASSIGN_SIGN_TYPE = "assignSignType";

    /**
     * 运单易损标示
     */
    private static final String CARGO_VULNERABLE_WAYBILL_SIGN ="2";

    /**
     * ucc配置
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 主产品扩展关联相关产品
     */
    private static final String PRODUCT_EXT_REF_PRODUCTNO= "refProductNo";

    /**
     * 快运订单标识
     */
    private static final String FREIGHT_SIGN= "2";

    /**
     * 仓配接货仓+快运主产品，订单标识
     */
    private static final String WAREHOUSE_MODE_FREIGHT_SIGN= "3";

    /**
     * 二手纸箱，外单增值服务KEY
     */
    private static final String SELF_PACKAGE_KEY= "selfPackage";
    /** 外单 特殊保障 key */
    private static final String SPECIAL_GUARANTEE = "specialSafeguard";
    /** 保障类型 */
    private static final String GUARANTEE_TYPE = "GuaranteeType";
    /** 冷藏生鲜运营保障 */
    private static final String COLD_FRESH_OPERATION = "coldFreshOperation";

    /**
     * 外单新增自定义服务
     */
    private static final int VALUE_ADDED_SOURCE_NEW_CUSTOM = 2;

    /**
     * 市场折扣类型：专业市场折扣
     */
    private static final String PROFESSIONAL_MARKET_DISCOUNT_TYPE = "3";


    private WaybillInfoMappingUtil() {
    }

    public ExpressOrderModelCreator transferOrder(WayBillQueryResponse wayBillQueryResponse, Map<String, ProductFacade> productFacadeMap, ExpressOrderModel snapShotModel) {

        return transferOrder(wayBillQueryResponse, productFacadeMap, snapShotModel, false);
    }

    /**
     * 构造订单信息
     *
     * @param wayBillQueryResponse
     * @return
     */
    public ExpressOrderModelCreator transferOrder(WayBillQueryResponse wayBillQueryResponse, Map<String, ProductFacade> productFacadeMap, ExpressOrderModel snapShotModel, boolean hasReset) {
        LOGGER.info("--transferOrder，wayBillQueryResponse:{},productFacadeMap:{},snapShotModel:{}",JSONUtils.beanToJSONDefault(wayBillQueryResponse),JSONUtils.beanToJSONDefault(productFacadeMap),JSONUtils.beanToJSONDefault(snapShotModel));
        if (wayBillQueryResponse == null) {
            return null;
        }
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        Map<String, String> map = new HashMap<>();
        map.put(ModifiedFieldEnum.ACTIVITY_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        map.put(ModifiedFieldEnum.CARGO_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        map.put(ModifiedFieldEnum.TICKET_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        map.put(ModifiedFieldEnum.DISCOUNT_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        map.put(ModifiedFieldEnum.PRODUCT_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        map.put(ModifiedFieldEnum.AGREEMENT_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        map.put(ModifiedFieldEnum.ATTACH_FEES.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        expressOrderModelCreator.setModifiedFields(map);
        //产品信息校验需要按业务身份区分处理，C2C和B2C签单返还处理逻辑不一致
        BusinessIdentityDto businessIdentityDto = new BusinessIdentityDto();
        businessIdentityDto.setBusinessUnit(snapShotModel.getOrderBusinessIdentity().getBusinessUnit());
        businessIdentityDto.setBusinessType(snapShotModel.getOrderBusinessIdentity().getBusinessType());
        businessIdentityDto.setBusinessStrategy(snapShotModel.getOrderBusinessIdentity().getBusinessStrategy());
        expressOrderModelCreator.setBusinessIdentity(businessIdentityDto);
        //修改时需清空的字段初始化
        expressOrderModelCreator.setClearFields(new ArrayList<>());
        /*基础信息*/
        buildBaseInfo(wayBillQueryResponse, expressOrderModelCreator, hasReset);
        /*渠道信息*/
        buildChannelInfo(wayBillQueryResponse, expressOrderModelCreator);
        /*产品服务信息*/
        buildProductInfo(wayBillQueryResponse, expressOrderModelCreator, productFacadeMap, snapShotModel);
        /*发货人信息*/
        buildConsignorInfo(wayBillQueryResponse, expressOrderModelCreator);
        /*收货人信息*/
        buildConsigneeInfo(wayBillQueryResponse, expressOrderModelCreator);
        /*货品信息*/
        buildCargoInfo(wayBillQueryResponse, expressOrderModelCreator,snapShotModel.getCargoDelegate());
        /*配送信息 依赖产品服务信息*/
        buildShipmentInfo(wayBillQueryResponse, expressOrderModelCreator, snapShotModel);
        /*财务信息*/
        buildFinanceInfo(wayBillQueryResponse, expressOrderModelCreator, snapShotModel);
        /*营销信息*/
        buildPromotionInfo(wayBillQueryResponse, expressOrderModelCreator, snapShotModel);

        /*客户信息*/
        buildCustomerInfo(wayBillQueryResponse, expressOrderModelCreator);

        /*履约信息*/
        buildFulfillmentInfo(wayBillQueryResponse, expressOrderModelCreator, snapShotModel.getCargoDelegate());

        if (CollectionUtils.isEmpty(expressOrderModelCreator.getPromotionInfo().getTicketInfos())) {
            LOGGER.info("运单号{},外单优惠券信息为空，标记为全删", wayBillQueryResponse.getDeliveryId());
            map.put(ModifiedFieldEnum.TICKET_INFOS.getCode(), ModifiedFieldValueEnum.ALL_DELETE.getCode());
        }
        return expressOrderModelCreator;
    }

    /**
     * @Description 设置基础信息
     * <AUTHOR>
     * @Date 21:53 2021/5/7
     * @Param wayBillResponse
     * @Param facadeRequest
     * @Return void
     * @Throws
     **/
    private void buildBaseInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, boolean hasReset) {
        expressOrderModelCreator.setOperateTime(new Date());//下单时间
        expressOrderModelCreator.setRemark(wayBillQueryResponse.getRemark());//订单备注

        if (hasReset) {

            ExtendMessageDTO extendMessageDTO = wayBillQueryResponse.getExtendMessageDTO();
            // 产品互改模式: 1-快运改快递，2-快递改快运。保持与运单一致
            if (extendMessageDTO != null && extendMessageDTO.getChangeProductMode() != null) {

                // 赋值主档扩展字段
                Map<String, String> extendMap = expressOrderModelCreator.getExtendProps();
                if (MapUtils.isEmpty(extendMap)) {
                    extendMap = new HashMap<>();
                }

                extendMap.put(OrderConstants.CHANGE_PRODUCT_MODE, String.valueOf(extendMessageDTO.getChangeProductMode()));

                // 快运改快递时，初始化商家端运单状态
                if (MagicCommonConstants.NUM_1.equals(extendMessageDTO.getChangeProductMode())) {
                    extendMap.put(OrderConstants.SELLER_EXTEND_STATUS, B2CExtendStatusEnum.LANG_SHOU_REN_WU_FEN_PEI.getExtendStatus());
                }

                expressOrderModelCreator.setExtendProps(extendMap);

            }

        }

    }


    /**
     * @Description 设置渠道信息
     * <AUTHOR>
     * @Date 21:53 2021/5/7
     * @Param wayBillResponse
     * @Param facadeRequest
     * @Return void
     * @Throws
     **/
    private void buildChannelInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator) {
        ChannelInfoDto channelInfoDto = new ChannelInfoDto();
        channelInfoDto.setChannelOperateTime(new Date());
        channelInfoDto.setSystemCaller(SystemCallerEnum.EXPRESS_OMS);
        expressOrderModelCreator.setChannelInfo(channelInfoDto);
    }

    /**
     * @Description 设置产品服务信息
     * <AUTHOR>
     * @Date 21:53 2021/5/7
     * @Param wayBillResponse
     * @Param facadeRequest
     * @Return void
     * @Throws
     **/
    //TODO 需要统一改下
    private void buildProductInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, Map<String, ProductFacade> productFacadeMap, ExpressOrderModel snapShotModel) {
        // 冷链B2C产品和快递产品不同，冷链特殊处理
        if(isCCB2C(snapShotModel, wayBillQueryResponse)){
            buildCCB2CProductInfo(wayBillQueryResponse, expressOrderModelCreator, productFacadeMap, snapShotModel);
            return;
        }
        // 快运产品和快递产品不同，快运特殊处理
        if ((UnitedB2CUtil.isUnitedB2C(snapShotModel) && isFreightByOrderMark(wayBillQueryResponse.getOrderMark()))
                || isFreight(snapShotModel)) {
            buildFreightProductInfo(wayBillQueryResponse, expressOrderModelCreator, productFacadeMap, snapShotModel);
            return;
        }

        // 冷链B2B产品和快递产品不同，冷链特殊处理
        if(snapShotModel.isCCB2B()){
            buildCCB2BProductInfo(wayBillQueryResponse, expressOrderModelCreator, productFacadeMap, snapShotModel);
            return;
        }

        //orderMark
        MarkUtil markUtil = new MarkUtil(wayBillQueryResponse.getOrderMark());
        //key=ProductNo
        Map<String, ProductInfoDto> productInfoDtoHashMap = new HashMap<>();
        //主产品
        ProductInfoDto mainProduct = new ProductInfoDto();
        mainProduct.setProductType(ServiceProductTypeEnum.MAIN_PRODUCT.getCode());
        if (ProductEnum.TKS.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.TKS.getDesc());
            mainProduct.setProductNo(ProductEnum.TKS.getCode());
        } else if (ProductEnum.TSSTC.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.TSSTC.getDesc());
            mainProduct.setProductNo(ProductEnum.TSSTC.getCode());
        } else if (ProductEnum.TSSCJ.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.TSSCJ.getDesc());
            mainProduct.setProductNo(ProductEnum.TSSCJ.getCode());
        } else if (ProductEnum.TCSP.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.TCSP.getDesc());
            mainProduct.setProductNo(ProductEnum.TCSP.getCode());
        } else if (ProductEnum.SXTK.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.SXTK.getDesc());
            mainProduct.setProductNo(ProductEnum.SXTK.getCode());
        } else if (ProductEnum.SXTH.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.SXTH.getDesc());
            mainProduct.setProductNo(ProductEnum.SXTH.getCode());
        } else if (ProductEnum.KDWXJ.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.KDWXJ.getDesc());
            mainProduct.setProductNo(ProductEnum.KDWXJ.getCode());
        } else if (ProductEnum.THBG.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.THBG.getDesc());
            mainProduct.setProductNo(ProductEnum.THBG.getCode());
        } else if (ProductEnum.HSD.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.HSD.getDesc());
            mainProduct.setProductNo(ProductEnum.HSD.getCode());
        } else if (ProductEnum.QCS.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.QCS.getDesc());
            mainProduct.setProductNo(ProductEnum.QCS.getCode());
        } else if (ProductEnum.SXZS.getSign().equals(String.valueOf(markUtil.charAt(55)))) {
            mainProduct.setProductName(ProductEnum.SXZS.getDesc());
            mainProduct.setProductNo(ProductEnum.SXZS.getCode());
        } else if (ProductEnum.THXJ.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.THXJ.getDesc());
            mainProduct.setProductNo(ProductEnum.THXJ.getCode());
        } else if (ProductEnum.DSTH.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.DSTH.getDesc());
            mainProduct.setProductNo(ProductEnum.DSTH.getCode());
        } else if (ProductEnum.TKBG.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.TKBG.getDesc());
            mainProduct.setProductNo(ProductEnum.TKBG.getCode());
        } else if (ProductEnum.SXZS.getSign().equals(String.valueOf(markUtil.charAt(55)))) {
            mainProduct.setProductName(ProductEnum.SXZS.getDesc());
            mainProduct.setProductNo(ProductEnum.SXZS.getCode());
        } else if (ProductEnum.GXS.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.GXS.getDesc());
            mainProduct.setProductNo(ProductEnum.GXS.getCode());
        } else if (ProductEnum.ECOMMERCE_RETURN.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
            mainProduct.setProductName(ProductEnum.ECOMMERCE_RETURN.getDesc());
            mainProduct.setProductNo(ProductEnum.ECOMMERCE_RETURN.getCode());
        } else {
            //默认主产品是特惠送
            mainProduct.setProductName(ProductEnum.THS.getDesc());
            mainProduct.setProductNo(ProductEnum.THS.getCode());
        }
        productInfoDtoHashMap.put(mainProduct.getProductNo(), mainProduct);

        //特快送运营模式
        if (ProductEnum.TKS.getCode().equals(mainProduct.getProductNo()) && !"0".equals(String.valueOf(markUtil.charAt(116)))) {
            ExpressDeliveryOperationModeEnum operationModeEnum = ExpressDeliveryOperationModeEnum.fromCode(Integer.parseInt(String.valueOf(markUtil.charAt(116))), ExpressDeliveryOperationModeType.TKS.name());
            if (mainProduct.getExtendProps() == null) {
                mainProduct.setExtendProps(new HashMap<>());
            }
            if (operationModeEnum != null) {
                mainProduct.getExtendProps().put(AddOnProductAttrEnum.OPERATION_MODE.getCode(), operationModeEnum.getRouteCode());
            } else {
                LOGGER.info("未识别出运营模式，ProductNo={},116={}", mainProduct.getProductNo(), markUtil.charAt(116));
            }
        }

        //生鲜特快送运营模式
        if (ProductEnum.SXTK.getCode().equals(mainProduct.getProductNo()) && !"0".equals(String.valueOf(markUtil.charAt(116)))) {
            ExpressDeliveryOperationModeEnum operationModeEnum = ExpressDeliveryOperationModeEnum.fromCode(Integer.parseInt(String.valueOf(markUtil.charAt(116))), ExpressDeliveryOperationModeType.SXTK.name());
            if (mainProduct.getExtendProps() == null) {
                mainProduct.setExtendProps(new HashMap<>());
            }
            if (operationModeEnum != null) {
                mainProduct.getExtendProps().put(AddOnProductAttrEnum.OPERATION_MODE.getCode(), operationModeEnum.getRouteCode());
            } else {
                LOGGER.info("未识别出运营模式，ProductNo={},116={}", mainProduct.getProductNo(), markUtil.charAt(116));
            }
        }

        // 特惠送运营模式
        if (isUseTHSOperationMode(mainProduct.getProductNo()) && !"0".equals(String.valueOf(markUtil.charAt(116)))) {
            ExpressDeliveryOperationModeEnum operationModeEnum = ExpressDeliveryOperationModeEnum.fromCode(Integer.parseInt(String.valueOf(markUtil.charAt(116))), ExpressDeliveryOperationModeType.THS.name());
            if (mainProduct.getExtendProps() == null) {
                mainProduct.setExtendProps(new HashMap<>());
            }
            if (operationModeEnum != null) {
                mainProduct.getExtendProps().put(AddOnProductAttrEnum.OPERATION_MODE.getCode(), operationModeEnum.getRouteCode());
            } else {
                LOGGER.info("未识别出运营模式，ProductNo={},116={}", mainProduct.getProductNo(), markUtil.charAt(116));
            }

        }

        // 生鲜特惠运营模式
        if (ProductEnum.SXTH.getCode().equals(mainProduct.getProductNo()) && !"0".equals(String.valueOf(markUtil.charAt(116)))) {
            ExpressDeliveryOperationModeEnum operationModeEnum = ExpressDeliveryOperationModeEnum.fromCode(Integer.parseInt(String.valueOf(markUtil.charAt(116))), ExpressDeliveryOperationModeType.SXTH.name());
            if (mainProduct.getExtendProps() == null) {
                mainProduct.setExtendProps(new HashMap<>());
            }
            if (operationModeEnum != null) {
                mainProduct.getExtendProps().put(AddOnProductAttrEnum.OPERATION_MODE.getCode(), operationModeEnum.getRouteCode());
            } else {
                LOGGER.info("未识别出运营模式，ProductNo={},116={}", mainProduct.getProductNo(), markUtil.charAt(116));
            }
        }

        // 如果是125模式。取原单的主产品覆盖主产品
        if (snapShotModel.isProductBusinessMode125()) {
            Product product = (Product) snapShotModel.getProductDelegate().getMainProduct();
            mainProduct = new ProductInfoDto();
            mainProduct.setProductNo(product.getProductNo());
            mainProduct.setProductAttrs(product.getProductAttrs());
            mainProduct.setProductType(product.getProductType());
            if (product.getPattern() != null) {
                PatternInfoDto pattern = new PatternInfoDto();
                mainProduct.setPatternInfoDto(pattern);
                pattern.setPatternNo(product.getPattern().getPatternNo());
                pattern.setPatternName(product.getPattern().getPatternName());
            }
            mainProduct.setProductName(product.getProductName());
            mainProduct.setPlanDeliveryTime(product.getPlanDeliveryTime());
            mainProduct.setParentNo(product.getParentNo());
            mainProduct.setExtendProps(product.getExtendProps());
            mainProduct.setProductExecutionResult(product.getProductExecutionResult());
            productInfoDtoHashMap.put(mainProduct.getProductNo(), mainProduct);
        }

        if (snapShotModel.isB2C() || snapShotModel.isB_O2O() || snapShotModel.isC2B()){
            b2cBuildAddProducts(wayBillQueryResponse,markUtil,productInfoDtoHashMap,productFacadeMap,mainProduct,expressOrderModelCreator);
        }
        if (snapShotModel.isC2C()){
            c2cBuildAddProducts(wayBillQueryResponse,markUtil,productInfoDtoHashMap,productFacadeMap,mainProduct);
        }

        //京准达增值服务
        if ("2".equals(String.valueOf(markUtil.charAt(113)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.J_ZHUN_DA.getCode());
            productInfoDto.setParentNo(mainProduct.getProductNo());
            productInfoDto.setProductName(AddOnProductEnum.J_ZHUN_DA.getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.J_ZHUN_DA.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            //派送开始、结束时间取值  派送时间间隔字段
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RECEIVE_START_TIME.getCode(), DateUtils.formatDatetime(wayBillQueryResponse.getShipmentStartTime()));
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RECEIVE_END_TIME.getCode(), DateUtils.formatDatetime(wayBillQueryResponse.getShipmentEndTime()));
            String receiveInterval = "";
            //派送时间间隔 A:60min B:120min C:30min D:300min
            if ("A".equals(String.valueOf(markUtil.charAt(16)))) {
                receiveInterval = "60";
            } else if ("B".equals(String.valueOf(markUtil.charAt(16)))) {
                receiveInterval = "120";
            } else if ("C".equals(String.valueOf(markUtil.charAt(16)))) {
                receiveInterval = "30";
            } else if ("D".equals(String.valueOf(markUtil.charAt(16)))) {
                receiveInterval = "300";
            } else {
                receiveInterval = productInfoDto.getProductAttrs().get(AddOnProductAttrEnum.RECEIVE_INTERVAL.getCode());
            }
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RECEIVE_INTERVAL.getCode(), receiveInterval);
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }


        //鸡毛信 2-追踪器 3-追踪箱
        if ("2".equals(String.valueOf(markUtil.charAt(92))) || "3".equals(String.valueOf(markUtil.charAt(92)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.FEATHER_LETTER.getCode());
            productInfoDto.setParentNo(mainProduct.getProductNo());
            productInfoDto.setProductName(AddOnProductEnum.FEATHER_LETTER.getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.FEATHER_LETTER.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            if ("2".equals(String.valueOf(markUtil.charAt(92)))) {
                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.DEVICE_TYPE.getCode(), AddOnProductAttrEnum.TRACE_EQUIMENT.getCode());
            }
            if ("3".equals(String.valueOf(markUtil.charAt(92)))) {
                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.DEVICE_TYPE.getCode(), AddOnProductAttrEnum.TRACE_BOX.getCode());
            }
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }

        //1、有包装服务 0、无包装服务（默认值）  和产品对过这个字段是B2B没有包装服务
//        if ("1".equals(String.valueOf(markUtil.charAt(72)))) {
//            ProductInfoDto productInfoDto = new ProductInfoDto();
//            setProductAttrs(productInfoDto,productFacadeMap,AddOnProductEnum.PACKAGE_SERVICE_TOC.getCode());
//            productInfoDto.setParentNo(mainProduct.getProductNo());
//            productInfoDto.setProductName(AddOnProductEnum.PACKAGE_SERVICE_TOC.getDesc());
//            productInfoDto.setProductNo(AddOnProductEnum.PACKAGE_SERVICE_TOC.getCode());
//            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
//            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
//        }

        //1、京尊达
        if ("1".equals(String.valueOf(markUtil.charAt(35)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.JZD.getCode());
            productInfoDto.setParentNo(mainProduct.getProductNo());
            productInfoDto.setProductName(AddOnProductEnum.JZD.getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.JZD.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }

        //指定签收
//        if ("2".equals(String.valueOf(markUtil.charAt(33))) || "3".equals(String.valueOf(markUtil.charAt(33))) || "4".equals(String.valueOf(markUtil.charAt(33)))) {
//            ProductInfoDto productInfoDto = new ProductInfoDto();
//            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.DESIGNATED_SIGN.getCode());
//            productInfoDto.setParentNo(mainProduct.getProductNo());
//            productInfoDto.setProductName(AddOnProductEnum.DESIGNATED_SIGN.getDesc());
//            productInfoDto.setProductNo(AddOnProductEnum.DESIGNATED_SIGN.getCode());
//            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
//            if ("2".equals(String.valueOf(markUtil.charAt(33)))) {
//                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.ASSIGN_SIGN.getCode(), "smsSign");
//            }
//            if ("3".equals(String.valueOf(markUtil.charAt(33)))) {
//                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.ASSIGN_SIGN.getCode(), "idCardSign");
//            }
//            if ("4".equals(String.valueOf(markUtil.charAt(33)))) {
//                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.ASSIGN_SIGN.getCode(), "combineSign");
//            }
//            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
//        }

        //协商再投
        if ("1".equals(String.valueOf(markUtil.charAt(5))) || "4".equals(String.valueOf(markUtil.charAt(5)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode());
            productInfoDto.setParentNo(mainProduct.getProductNo());
            if ("1".equals(String.valueOf(markUtil.charAt(5)))) {
                productInfoDto.getProductAttrs().put("rejectAuditType", "once");
                //TODO 随B2C上线再进行逻辑调整
                //productInfoDto.getProductAttrs().put("rejectAuditNumbers", "1");
            } else if ("4".equals(String.valueOf(markUtil.charAt(5)))) {
                productInfoDto.getProductAttrs().put("rejectAuditType", "repeatedly");
            }
            productInfoDto.getProductAttrs().put("rejectAuditNumbers", "0");
            productInfoDto.setProductName(AddOnProductEnum.NEGOTIATION_REDELIVERY.getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }

        //改址服务
        if ("4".equals(String.valueOf(markUtil.charAt(103)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.READDRESS.getCode());
            productInfoDto.setParentNo(mainProduct.getProductNo());
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.READDRESS_TYPE.getCode(), "sameSite");
            productInfoDto.setProductName(AddOnProductEnum.READDRESS.getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.READDRESS.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }

        //揽收防撕码收集
        if ("1".equals(String.valueOf(markUtil.charAt(52)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.PROOF_PICKTEAR_CODE.getCode());
            productInfoDto.setParentNo(mainProduct.getProductNo());
            productInfoDto.setProductName(AddOnProductEnum.PROOF_PICKTEAR_CODE.getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.PROOF_PICKTEAR_CODE.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            productInfoDto.getProductAttrs().put("pickTearCode", "pupTearCodeBox");
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }

        //微笑面单
        if (!"0".equals(String.valueOf(markUtil.charAt(37))) || !"0".equals(String.valueOf(markUtil.charAt(47)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.SMILE_EXPRESS_SHEET.getCode());
            productInfoDto.setParentNo(mainProduct.getProductNo());
            productInfoDto.setProductName(AddOnProductEnum.SMILE_EXPRESS_SHEET.getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.SMILE_EXPRESS_SHEET.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            List<String> context = new ArrayList<>();
            String receiverSign = String.valueOf(markUtil.charAt(37));
            String sendSign = String.valueOf(markUtil.charAt(47));
            if (!"0".equals(receiverSign)) {
                HiddenContentEnum hiddenContent = HiddenContentEnum.getHidden(receiverSign);
                if (hiddenContent != null) {
                    context.addAll(Arrays.asList(hiddenContent.getHiddenReceiverContent().split(",")));
                }
            }
            if (!"0".equals(sendSign)) {
                HiddenContentEnum hiddenContent = HiddenContentEnum.getHidden(sendSign);

                if (hiddenContent != null) {
                    context.addAll(Arrays.asList(hiddenContent.getHiddenSenderContent().split(",")));
                }
            }
            //微笑面单属性格式拼接
            productInfoDto.getProductAttrs().put("hiddenContent", JSONUtils.beanToJSONDefault(context));
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }

        //后来由产品中心定义的标准增值产品都是从这个集合里取
        if (CollectionUtils.isNotEmpty(wayBillQueryResponse.getValueAddedServiceDTOList())) {
            // 记录是否需要删除协议信息
            boolean deleteAgreement = true;
            for (ValueAddedServiceDTO dto : wayBillQueryResponse.getValueAddedServiceDTOList()) {
                //判空
                if(null == dto){
                    continue;
                }
                //主产品扩展关联产品信息
                if(expressUccConfigCenter.isSyncMainProductExtRefWhite(dto.getType())){
                    Map<String, String> extendProps = Optional.ofNullable(mainProduct.getExtendProps()).orElse(new HashMap<>());
                    extendProps.put(PRODUCT_EXT_REF_PRODUCTNO,dto.getType());
                    mainProduct.setExtendProps(extendProps);
                }
                //增值服务
                if (expressUccConfigCenter.isSyncNewAddOnProductWhite(dto.getType())) {
                    if (isCombineSign(dto)) {
                        continue;
                    }
                    ProductInfoDto productInfoDto = new ProductInfoDto();
                    //所属主产品编码
                    productInfoDto.setParentNo(mainProduct.getProductNo());
                    //增值产品编码
                    productInfoDto.setProductNo(dto.getType());
                    //增值产品名称
                    productInfoDto.setProductName(AddOnProductEnum.of(dto.getType()).getDesc());
                    productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                    //增值产品属性
                    productInfoDto.setProductAttrs(dto.getOtherParams());
                    productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
                } else if (DEDUCTIBLE_SERVICE.equals(dto.getType())) {
                    Map<String, String> otherParams = dto.getOtherParams();
                    if (MapUtils.isEmpty(otherParams)) {
                        continue;
                    }
                    List<AgreementInfoDto> agreementInfoDtos = otherParams.values().stream()
                        .filter(StringUtils::isNotBlank)
                        .map(jsonStr -> JSONUtils.jsonToBean(jsonStr, WaybillInfoMappingUtil.AgreementInfo.class))
                        .filter(Objects::nonNull)
                        .map(WaybillInfoMappingUtil.AgreementInfo::toAgreementInfoDto)
                        .collect(Collectors.toList());
                    expressOrderModelCreator.setAgreementInfoDtos(agreementInfoDtos);
                }
            }

            if (CollectionUtils.isEmpty(expressOrderModelCreator.getAgreementInfoDtos())) {
                expressOrderModelCreator.getModifiedFields().put(ModifiedFieldEnum.AGREEMENT_INFOS.getCode(), ModifiedFieldValueEnum.ALL_DELETE.getCode());
            }

        }

        expressOrderModelCreator.setProducts(new ArrayList<>(productInfoDtoHashMap.values()));
    }

    /**
     * 判断valueAddedService是否是指定签收中的合并签收
     * @param dto
     * @return
     */
    private static boolean isCombineSign(ValueAddedServiceDTO dto) {
        return AddOnProductEnum.DESIGNATED_SIGN.getCode().equals(dto.getType())
                && MapUtils.isNotEmpty(dto.getOtherParams())
                && COMBINE_SIGN.equals(dto.getOtherParams().get(ASSIGN_SIGN_TYPE));
    }

    private void b2cBuildAddProducts(WayBillQueryResponse wayBillQueryResponse, MarkUtil markUtil,Map<String, ProductInfoDto> productInfoDtoHashMap,
                                     Map<String, ProductFacade> productFacadeMap, ProductInfoDto mainProduct,ExpressOrderModelCreator expressOrderModelCreator){
        //单单保，扩展字段保存
        Map<String,String> extendMap = new HashMap<>();
        Map<String,String> customerInfoExtendProps = new HashMap<>();
        if (expressOrderModelCreator.getExtendProps() != null){
            extendMap = expressOrderModelCreator.getExtendProps();
            if (StringUtils.isNotBlank(extendMap.get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS))){
                customerInfoExtendProps = JSONUtils.jsonToMap(extendMap.get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
            }
        }
        if ("1".equals(String.valueOf(markUtil.charAt(46)))
                || "3".equals(String.valueOf(markUtil.charAt(46)))
                || "5".equals(String.valueOf(markUtil.charAt(46)))) {
            customerInfoExtendProps.put(OrderConstants.SINGLE_INSURANCE,OrderConstants.SINGLE_INSURANCE_VALUE_1);
        }else {
            customerInfoExtendProps.put(OrderConstants.SINGLE_INSURANCE,OrderConstants.SINGLE_INSURANCE_VALUE_0);
        }
        extendMap.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS,JSONUtils.mapToJson(customerInfoExtendProps));
        expressOrderModelCreator.setExtendProps(extendMap);

        // 是否保价
        if (wayBillQueryResponse.getGuaranteeValue() == 1) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.getInsuredValueCode().get(0));
            productInfoDto.setParentNo(mainProduct.getProductNo());
            if (ProductEnum.SXTK.getSign().equals(String.valueOf(markUtil.charAt(31)))
                    || ProductEnum.SXTH.getSign().equals(String.valueOf(markUtil.charAt(31)))){
                productInfoDto.setProductNo(AddOnProductEnum.LL_ZZ_BJ.getCode());
                productInfoDto.setProductName(AddOnProductEnum.LL_ZZ_BJ.getDesc());
            }else {
                productInfoDto.setProductNo(AddOnProductEnum.INSURED_VALUE_TOC.getCode());
                productInfoDto.setProductName(AddOnProductEnum.INSURED_VALUE_TOC.getDesc());
            }
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            //将运单信息-由产品中心定义的标准增值产品的要素补充到 当前需要同步的增值服务
            addProductAttrsByValueAddedService(productInfoDto, wayBillQueryResponse.getValueAddedServiceDTOList());
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.GUARANTEE_VALUE.getCode(), String.valueOf(wayBillQueryResponse.getGuaranteeValueAmount()));
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }

        //是否代收货款
        if (wayBillQueryResponse.getCollectionValue() == 1) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            productInfoDto.setParentNo(mainProduct.getProductNo());
            if (ProductEnum.SXTK.getSign().equals(String.valueOf(markUtil.charAt(31)))
                    || ProductEnum.SXTH.getSign().equals(String.valueOf(markUtil.charAt(31)))){
                productInfoDto.setProductNo(AddOnProductEnum.LL_ZZ_DSHK.getCode());
                productInfoDto.setProductName(AddOnProductEnum.LL_ZZ_DSHK.getDesc());
            }else {
                productInfoDto.setProductNo(AddOnProductEnum.JDL_COD_TOC.getCode());
                productInfoDto.setProductName(AddOnProductEnum.JDL_COD_TOC.getDesc());
            }
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            Map<String ,String> productAttrs = productInfoDto.getProductAttrs();
            if (productAttrs == null){
                productAttrs = new HashMap<>();
            }
            productAttrs.put(AddOnProductAttrEnum.COD.getCode(), String.valueOf(wayBillQueryResponse.getCollectionMoney()));
            productInfoDto.setProductAttrs(productAttrs);
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }

        //签单返还 -- 调整为标准增值服务同步
        /*if ("1".equals(String.valueOf(markUtil.charAt(4))) || "3".equals(String.valueOf(markUtil.charAt(4))) || "4".equals(String.valueOf(markUtil.charAt(4)))
            || "5".equals(String.valueOf(markUtil.charAt(4))) || "6".equals(String.valueOf(markUtil.charAt(4)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.getSignReturnCode().get(0));
            productInfoDto.setParentNo(mainProduct.getProductNo());
            if (ProductEnum.SXTK.getSign().equals(String.valueOf(markUtil.charAt(31)))
                    || ProductEnum.SXTH.getSign().equals(String.valueOf(markUtil.charAt(31)))){
                productInfoDto.setProductNo(AddOnProductEnum.LL_ZZ_QDFH.getCode());
                productInfoDto.setProductName(AddOnProductEnum.LL_ZZ_QDFH.getDesc());
            }else {
                productInfoDto.setProductNo(AddOnProductEnum.SIGN_RETURN_TOC.getCode());
                productInfoDto.setProductName(AddOnProductEnum.SIGN_RETURN_TOC.getDesc());
            }
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            //B2C需要补齐服务要素-签单返还模式
            if ("1".equals(String.valueOf(markUtil.charAt(4)))) {
                JSONArray reReceiveVal = new JSONArray();
                reReceiveVal.add("written");
                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE.getCode(), reReceiveVal.toString());
            } else if ("3".equals(String.valueOf(markUtil.charAt(4)))) {
                JSONArray reReceiveVal = new JSONArray();
                reReceiveVal.add("electronic");
                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE.getCode(), reReceiveVal.toString());
            } else if ("4".equals(String.valueOf(markUtil.charAt(4)))) {
                JSONArray reReceiveVal = new JSONArray();
                reReceiveVal.add("written");
                reReceiveVal.add("electronic");
                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE.getCode(), reReceiveVal.toString());
                // 链上签
            } else if ("5".equals(String.valueOf(markUtil.charAt(4)))) {
                JSONArray reReceiveVal = new JSONArray();
                reReceiveVal.add("signOnline");
                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE.getCode(), reReceiveVal.toString());
                // 链上签+纸质
            } else if ("6".equals(String.valueOf(markUtil.charAt(4)))) {
                JSONArray reReceiveVal = new JSONArray();
                reReceiveVal.add("written");
                reReceiveVal.add("signOnline");
                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE.getCode(), reReceiveVal.toString());
            }
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }*/

        //保温箱
        if ("1".equals(String.valueOf(markUtil.charAt(134)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            productInfoDto.setParentNo(mainProduct.getProductNo());
            productInfoDto.setProductName(AddOnProductEnum.COOLER_BOX.getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.COOLER_BOX.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }
    }

    private void c2cBuildAddProducts(WayBillQueryResponse wayBillQueryResponse, MarkUtil markUtil,Map<String, ProductInfoDto> productInfoDtoHashMap,
                                     Map<String, ProductFacade> productFacadeMap, ProductInfoDto mainProduct){
        //保价:产品中心将普通保价和生鲜保价分成了两个增值服务编码，故需调整，由于线上还是一个保价编码，故需兼容线上老逻辑
        if (wayBillQueryResponse.getGuaranteeValue() == 1) {
            //C2C订单，先从ValueAddedServiceDTOList里取保价信息
            ValueAddedServiceDTO valueAddedServiceDTO = getWaybillValueAddedServiceDTO(AddOnProductEnum.INSURED_VALUE_TOC.getCode(), wayBillQueryResponse.getValueAddedServiceDTOList());
            if (valueAddedServiceDTO == null) {
                //保价信息为空，看看是否有生鲜保价
                valueAddedServiceDTO = getWaybillValueAddedServiceDTO(AddOnProductEnum.LL_ZZ_BJ.getCode(), wayBillQueryResponse.getValueAddedServiceDTOList());
            }

            if(valueAddedServiceDTO == null){
                //看看是否有全额保
                valueAddedServiceDTO = getWaybillValueAddedServiceDTO(AddOnProductEnum.INSURED_VALUE_FULL.getCode(), wayBillQueryResponse.getValueAddedServiceDTOList());
            }

            if (valueAddedServiceDTO != null) {
                ProductInfoDto productInfoDto = new ProductInfoDto();
                //所属主产品编码
                productInfoDto.setParentNo(mainProduct.getProductNo());
                //增值产品编码
                productInfoDto.setProductNo(valueAddedServiceDTO.getType());
                //增值产品名称
                productInfoDto.setProductName(AddOnProductEnum.of(valueAddedServiceDTO.getType()).getDesc());
                productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                //增值产品属性
                productInfoDto.setProductAttrs(valueAddedServiceDTO.getOtherParams());
                productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
            } else {
                //兼容线上老逻辑，如果ValueAddedServiceDTOList未取到保价信息，则按普通保价处理
                ProductInfoDto productInfoDto = new ProductInfoDto();
                setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.INSURED_VALUE_TOC.getCode());
                productInfoDto.setParentNo(mainProduct.getProductNo());
                productInfoDto.setProductName(AddOnProductEnum.INSURED_VALUE_TOC.getDesc());
                productInfoDto.setProductNo(AddOnProductEnum.INSURED_VALUE_TOC.getCode());
                productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.GUARANTEE_VALUE.getCode(), String.valueOf(wayBillQueryResponse.getGuaranteeValueAmount()));
                productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
            }
        }

        //是否代收货款
        if (wayBillQueryResponse.getCollectionValue() == 1) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.JDL_COD_TOC.getCode());
            productInfoDto.setParentNo(mainProduct.getProductNo());
            productInfoDto.setProductName(AddOnProductEnum.getCod().get(0).getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.JDL_COD_TOC.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.COD.getCode(), String.valueOf(wayBillQueryResponse.getCollectionMoney()));
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }

        //签单返还--调整为标准增值服务同步
        /*if ("1".equals(String.valueOf(markUtil.charAt(4))) || "3".equals(String.valueOf(markUtil.charAt(4))) || "4".equals(String.valueOf(markUtil.charAt(4)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            //C2C签单返还,目前只能识别出是否有签单返还增值服务，增值属性识别不出，配置到白名单中
            setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.SIGN_RETURN_TOC.getCode());
            productInfoDto.setParentNo(mainProduct.getProductNo());
            productInfoDto.setProductName(AddOnProductEnum.SIGN_RETURN_TOC.getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.SIGN_RETURN_TOC.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }*/

        //保温箱
        if ("1".equals(String.valueOf(markUtil.charAt(134)))) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            productInfoDto.setParentNo(mainProduct.getProductNo());
            productInfoDto.setProductName(AddOnProductEnum.COOLER_BOX.getDesc());
            productInfoDto.setProductNo(AddOnProductEnum.COOLER_BOX.getCode());
            productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            //C2C订单，保温箱属性从ValueAddedServiceDTOList里取
            ValueAddedServiceDTO valueAddedServiceDTO = getWaybillValueAddedServiceDTO(AddOnProductEnum.COOLER_BOX.getCode(), wayBillQueryResponse.getValueAddedServiceDTOList());
            if (valueAddedServiceDTO != null && valueAddedServiceDTO.getOtherParams() != null) {
                String boxNum = valueAddedServiceDTO.getOtherParams().get(WAYBILL_BOX_NUM_KEY);
                if (StringUtils.isNotBlank(boxNum)) {
                    HashMap<String, String> boxAttrs = new HashMap<>();
                    boxAttrs.put(PRODUCT_BOX_NUM_KEY, boxNum);
                    productInfoDto.setProductAttrs(boxAttrs);
                }
            }
            if (productInfoDto.getProductAttrs() == null) {
                LOGGER.info("数据同步未从外单获取到保温箱数量，waybillNo={}", wayBillQueryResponse.getOrderId());
            }
            productInfoDtoHashMap.put(productInfoDto.getProductNo(), productInfoDto);
        }
    }

    /**
     * 获取外单增值服务
     *
     * @param productNo
     * @param valueAddedServiceDTOList
     * @return
     */
    private ValueAddedServiceDTO getWaybillValueAddedServiceDTO(String productNo, List<ValueAddedServiceDTO> valueAddedServiceDTOList) {
        if (StringUtils.isBlank(productNo)) {
            return null;
        }
        if (CollectionUtils.isEmpty(valueAddedServiceDTOList)) {
            return null;
        }
        for (ValueAddedServiceDTO dto : valueAddedServiceDTOList) {
            if (dto != null && productNo.equals(dto.getType())) {
                return dto;
            }
        }
        return null;
    }

    /**
     * @Description 设置发货人信息
     * <AUTHOR>
     * @Date 21:53 2021/5/7
     * @Param wayBillResponse
     * @Param facadeRequest
     * @Return void
     * @Throws
     **/

    private void buildConsignorInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator) {
        ConsignorInfoDto consignorInfoDto = new ConsignorInfoDto();
        AddressInfoDto addressInfo = new AddressInfoDto();
        consignorInfoDto.setConsignorName(wayBillQueryResponse.getSenderName());//寄件人姓名
        consignorInfoDto.setConsignorMobile(wayBillQueryResponse.getSenderMobile());//寄件人手机号
        consignorInfoDto.setConsignorPhone(wayBillQueryResponse.getSenderTel());//寄件人电话
        consignorInfoDto.setConsignorIdNo(wayBillQueryResponse.getSenderIdNumber());//寄件人证件号码
        consignorInfoDto.setConsignorCompany(wayBillQueryResponse.getSenderCompany());//寄件人公司
        //addressInfo.setAddress(wayBillQueryResponse.getSenderAddress());//寄件人地址
        //addressInfo.setProvinceNo(wayBillQueryResponse.getSenderProvinceId() != null ? String.valueOf(wayBillQueryResponse.getSenderProvinceId()) : null);//寄件省ID
        //addressInfo.setProvinceName(wayBillQueryResponse.getSenderProvinceName());//寄件省名称
        //addressInfo.setCityNo(wayBillQueryResponse.getSenderCityId() != null ? String.valueOf(wayBillQueryResponse.getSenderCityId()) : null);//寄件市ID
        //addressInfo.setCityName(wayBillQueryResponse.getSenderCityName());//寄件市
        //addressInfo.setCountyNo(wayBillQueryResponse.getSenderCountyId() != null ? String.valueOf(wayBillQueryResponse.getSenderCountyId()) : null);//寄件县ID
        //addressInfo.setCountyName(wayBillQueryResponse.getSenderCountyName());//寄件县
        //addressInfo.setTownNo(wayBillQueryResponse.getSenderTownId() != null ? String.valueOf(wayBillQueryResponse.getSenderTownId()) : null);//寄件镇ID
        //addressInfo.setTownName(wayBillQueryResponse.getSenderTownName());//寄件镇
        addressInfo.setAddressGis(wayBillQueryResponse.getSenderAddress());//gis寄件人地址
        addressInfo.setProvinceNoGis(wayBillQueryResponse.getSenderProvinceId() != null ? String.valueOf(wayBillQueryResponse.getSenderProvinceId()) : null);//gis寄件省ID
        addressInfo.setProvinceNameGis(wayBillQueryResponse.getSenderProvinceName());//gis寄件省名称
        addressInfo.setCityNoGis(wayBillQueryResponse.getSenderCityId() != null ? String.valueOf(wayBillQueryResponse.getSenderCityId()) : null);//gis寄件市ID
        addressInfo.setCityNameGis(wayBillQueryResponse.getSenderCityName());//gis寄件市
        addressInfo.setCountyNoGis(wayBillQueryResponse.getSenderCountyId() != null ? String.valueOf(wayBillQueryResponse.getSenderCountyId()) : null);//gis寄件县ID
        addressInfo.setCountyNameGis(wayBillQueryResponse.getSenderCountyName());//gis寄件县
        addressInfo.setTownNoGis(wayBillQueryResponse.getSenderTownId() != null ? String.valueOf(wayBillQueryResponse.getSenderTownId()) : null);//gis寄件镇ID
        addressInfo.setTownNameGis(wayBillQueryResponse.getSenderTownName());//gis寄件镇
        consignorInfoDto.setAddressInfoDto(addressInfo);
        expressOrderModelCreator.setConsignorInfo(consignorInfoDto);
    }

    /**
     * @Description 设置收货人信息
     * <AUTHOR>
     * @Date 21:53 2021/5/7
     * @Param wayBillResponse
     * @Param facadeRequest
     * @Return void
     * @Throws
     **/
    private void buildConsigneeInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator) {
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
        AddressInfoDto addressInfo = new AddressInfoDto();
        consigneeInfoDto.setConsigneeName(wayBillQueryResponse.getReceiveName());//收件人姓名
        consigneeInfoDto.setConsigneeMobile(wayBillQueryResponse.getReceiveMobile());//收件人手机号
        consigneeInfoDto.setConsigneePhone(wayBillQueryResponse.getReceiveTel());//收件人电话
        consigneeInfoDto.setConsigneeCompany(wayBillQueryResponse.getReceiveCompany());//收件人公司
        //addressInfo.setAddress(wayBillQueryResponse.getReceiveAddress());//收件人地址
        addressInfo.setAddressGis(wayBillQueryResponse.getReceiveAddress());//gis收件人地址
        //addressInfo.setProvinceNo(wayBillQueryResponse.getProvinceId() != null ? String.valueOf(wayBillQueryResponse.getProvinceId()) : null);//收件省ID
        addressInfo.setProvinceNoGis(wayBillQueryResponse.getProvinceId() != null ? String.valueOf(wayBillQueryResponse.getProvinceId()) : null);//gis收件省ID
        //addressInfo.setProvinceName(wayBillQueryResponse.getProvince());//收件省名称
        addressInfo.setProvinceNameGis(wayBillQueryResponse.getProvince());//gis收件省名称
        //addressInfo.setCityNo(wayBillQueryResponse.getCityId() != null ? String.valueOf(wayBillQueryResponse.getCityId()) : null);//收件市ID
        addressInfo.setCityNoGis(wayBillQueryResponse.getCityId() != null ? String.valueOf(wayBillQueryResponse.getCityId()) : null);//收件市ID
        //addressInfo.setCityName(wayBillQueryResponse.getCity());//收件市
        addressInfo.setCityNameGis(wayBillQueryResponse.getCity());//gis收件市
        //addressInfo.setCountyNo(wayBillQueryResponse.getCountyId() != null ? String.valueOf(wayBillQueryResponse.getCountyId()) : null);//收件县ID
        addressInfo.setCountyNoGis(wayBillQueryResponse.getCountyId() != null ? String.valueOf(wayBillQueryResponse.getCountyId()) : null);//gis收件县ID
        //addressInfo.setCountyName(wayBillQueryResponse.getCounty());//收件县
        addressInfo.setCountyNameGis(wayBillQueryResponse.getCounty());//gis收件县
        //addressInfo.setTownNo(wayBillQueryResponse.getTownId() != null ? String.valueOf(wayBillQueryResponse.getTownId()) : null);//收件镇ID
        addressInfo.setTownNoGis(wayBillQueryResponse.getTownId() != null ? String.valueOf(wayBillQueryResponse.getTownId()) : null);//gis收件镇ID
        //addressInfo.setTownName(wayBillQueryResponse.getTown());//收件镇
        addressInfo.setTownNameGis(wayBillQueryResponse.getTown());//gis收件镇
        consigneeInfoDto.setAddressInfoDto(addressInfo);
        expressOrderModelCreator.setConsigneeInfo(consigneeInfoDto);
    }

    /**
     * @Description 设置货品信息
     * <AUTHOR>
     * @Date 21:53 2021/5/7
     * @Param wayBillResponse
     * @Param facadeRequest
     * @Return void
     * @Throws
     **/
    public void buildCargoInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, CargoDelegate snapShotCargoDelegate) {
        if (null != snapShotCargoDelegate && snapShotCargoDelegate.isPackageAsCargo()) {
            // 订单上是包裹信息，需要取外单的包裹信息同步
            // 2023-08-17 linhengbo & chenhuaiyu
            // 确认，如果是包裹信息，跳过货品维度的数据同步
            expressOrderModelCreator.getModifiedFields().remove(ModifiedFieldEnum.CARGO_INFOS.getCode());
            return;
        }

        List<CargoInfoDto> cargoInfoDtoList = new ArrayList<>();
        CargoInfoDto cargoInfoDto = new CargoInfoDto();
        // 货品信息在托寄物维度上仅有一个对象
        Cargo cargo = (Cargo) snapShotCargoDelegate.firstCargo();

        //在原单的货品信息基础上修改
        cargoInfoDtoOf(cargoInfoDto,cargo);
        //商品名称
        if (StringUtils.isNotEmpty(wayBillQueryResponse.getGoods())){
            cargoInfoDto.setCargoName(wayBillQueryResponse.getGoods());
        }
        //货品类型
        if (wayBillQueryResponse.getCargoType() != null){
            cargoInfoDto.setCargoType(String.valueOf(wayBillQueryResponse.getCargoType()));
        }
        //取件备注(留言)
        if (StringUtils.isNotBlank(wayBillQueryResponse.getPickUpRemark())) {
            cargoInfoDto.setCargoRemark(wayBillQueryResponse.getPickUpRemark());
        }
        //包裹数量
        if (wayBillQueryResponse.getPackageCount() != null) {
            QuantityInfoDto quantity = new QuantityInfoDto();
            quantity.setValue(new BigDecimal(wayBillQueryResponse.getPackageCount().toString()));
            quantity.setUnit(cargo.getCargoQuantity().getUnit());
            cargoInfoDto.setCargoQuantityInfo(quantity);
        }
        WeightInfoDto weight = new WeightInfoDto();
        weight.setUnit(WeightTypeEnum.KG);

        //重量单位
        if (StringUtils.isNotEmpty(wayBillQueryResponse.getWeightUnit())) {
            if (WeightTypeEnum.G.getCode().equalsIgnoreCase(wayBillQueryResponse.getWeightUnit())) {
                weight.setUnit(WeightTypeEnum.G);
            } else if (WeightTypeEnum.KG.getCode().equalsIgnoreCase(wayBillQueryResponse.getWeightUnit())) {
                weight.setUnit(WeightTypeEnum.KG);
            } else if (WeightTypeEnum.T.getCode().equalsIgnoreCase(wayBillQueryResponse.getWeightUnit())) {
                weight.setUnit(WeightTypeEnum.T);
            } else {
                weight.setUnit(WeightTypeEnum.KG);
            }
        }
        if (wayBillQueryResponse.getWeight() != null) {
            weight.setValue(TypeConversion.stringToBigDecimal(wayBillQueryResponse.getWeight().toString(), 4, null));//重量值
            cargoInfoDto.setCargoWeight(weight);//重量
        }

        //体积
        VolumeInfoDto volume = new VolumeInfoDto();
        volume.setValue(new BigDecimal(0));
        volume.setUnit(VolumeTypeEnum.CM3);
        if (wayBillQueryResponse.getVolume() != null) {
            volume.setValue(TypeConversion.stringToBigDecimal(wayBillQueryResponse.getVolume().toString(), 4, null));
            cargoInfoDto.setCargoVolume(volume);//体积
        }

        //体积长、宽、高、单位
        if (wayBillQueryResponse.getVolumeHeight() != null || wayBillQueryResponse.getVolumeLong() != null
                || wayBillQueryResponse.getVolumeWidth() != null || StringUtils.isNotEmpty(wayBillQueryResponse.getVolumeUnit())) {
            DimensionInfoDto cargoDimension = new DimensionInfoDto();
            if (wayBillQueryResponse.getVolumeHeight() != null) {
                cargoDimension.setHeight(TypeConversion.stringToBigDecimal(wayBillQueryResponse.getVolumeHeight().toString(), 4, null));
            }
            if (wayBillQueryResponse.getVolumeLong() != null) {
                cargoDimension.setLength(TypeConversion.stringToBigDecimal(wayBillQueryResponse.getVolumeLong().toString(), 4, null));
            }
            if (wayBillQueryResponse.getVolumeWidth() != null) {
                cargoDimension.setWidth(TypeConversion.stringToBigDecimal(wayBillQueryResponse.getVolumeWidth().toString(), 4, null));
            }
            cargoDimension.setUnit(LengthTypeEnum.CM);

            if (StringUtils.isNotEmpty(wayBillQueryResponse.getVolumeUnit())) {
                for (LengthTypeEnum lengthTypeEnum : LengthTypeEnum.values()) {
                    if (lengthTypeEnum.getCode().equalsIgnoreCase(wayBillQueryResponse.getVolumeUnit())) {
                        cargoDimension.setUnit(lengthTypeEnum);
                    }
                }
            }
            cargoInfoDto.setDimensionInfo(cargoDimension);//货品尺寸
        }
        MarkUtil markUtil = new MarkUtil(wayBillQueryResponse.getOrderMark());
        //是否易损
        if (CARGO_VULNERABLE_WAYBILL_SIGN.equals(String.valueOf(markUtil.charAt(135)))) {
            cargoInfoDto.setCargoVulnerable(CargoVulnerableEnum.YES.getCode());
        }
        if (!expressUccConfigCenter.isNotUpdatePackagingAttributesSwitch()) {
            ExtendMessageDTO extendMessageDTO = wayBillQueryResponse.getExtendMessageDTO();
            //包装属性
            if (extendMessageDTO != null) {
                String packagingAttributes = extendMessageDTO.getPackagingAttributes();
                Map<String, String> extendProps = cargoInfoDto.getExtendProps() != null ? cargoInfoDto.getExtendProps() : new HashMap<>();
                if (packagingAttributes != null) {
                    extendProps.put(OrderConstants.PACKAGING_ATTRIBUTES, packagingAttributes);
                }
                cargoInfoDto.setExtendProps(extendProps);
            }
        }
        cargoInfoDtoList.add(cargoInfoDto);
        expressOrderModelCreator.setCargoInfos(cargoInfoDtoList);
    }

    /**
     * 申请接单入参货品信息数据对象转换成接单领域模型
     *
     * @param cargoInfoDto
     * @param cargo
     */
    private void cargoInfoDtoOf(CargoInfoDto cargoInfoDto, Cargo cargo) {
        //货品名称
        cargoInfoDto.setCargoName(cargo.getCargoName());
        //货品编码
        cargoInfoDto.setCargoNo(cargo.getCargoNo());
        //货品类型
        cargoInfoDto.setCargoType(cargo.getCargoType());
        //货品体积
        Optional.ofNullable(cargo.getCargoVolume()).ifPresent(volumeInfo -> {
            VolumeInfoDto volumeInfoDto = new VolumeInfoDto();
            volumeInfoDto.setValue(volumeInfo.getValue());
            volumeInfoDto.setUnit(volumeInfo.getUnit());
            cargoInfoDto.setCargoVolume(volumeInfoDto);
        });

        //货品重量
        Optional.ofNullable(cargo.getCargoWeight()).ifPresent(weightInfo -> {
            WeightInfoDto weightInfoDto = new WeightInfoDto();
            weightInfoDto.setValue(weightInfo.getValue());
            weightInfoDto.setUnit(weightInfo.getUnit());
            cargoInfoDto.setCargoWeight(weightInfoDto);
        });

        //货品数量
        Optional.ofNullable(cargo.getCargoQuantity()).ifPresent(cargoQuantity -> {
            QuantityInfoDto quantityInfoDto = new QuantityInfoDto();
            quantityInfoDto.setValue(cargoQuantity.getValue());
            quantityInfoDto.setUnit(cargoQuantity.getUnit());
            cargoInfoDto.setCargoQuantityInfo(quantityInfoDto);
        });
        //货品内件数量
        Optional.ofNullable(cargo.getCargoInnerQuantity()).ifPresent(cargoInnerQuantity -> {
            QuantityInfoDto quantityInfoDto = new QuantityInfoDto();
            quantityInfoDto.setValue(cargoInnerQuantity);
            quantityInfoDto.setUnit(cargo.getCargoQuantity().getUnit());
            cargoInfoDto.setCargoInnerQuantityInfo(quantityInfoDto);
        });
        //长宽高未做聚合
        Optional.ofNullable(cargo.getCargoDimension()).ifPresent(dimensionInfo -> {
            DimensionInfoDto dimensionInfoDto = new DimensionInfoDto();
            dimensionInfoDto.setHeight(dimensionInfo.getHeight());
            dimensionInfoDto.setWidth(dimensionInfo.getWidth());
            dimensionInfoDto.setLength(dimensionInfo.getLength());
            dimensionInfoDto.setUnit(dimensionInfo.getUnit());
            cargoInfoDto.setDimensionInfo(dimensionInfoDto);
        });
        //货品备注
        cargoInfoDto.setCargoRemark(cargo.getCargoRemark());
        //清真易污染标识
        Optional.ofNullable(cargo.getPolluteSign())
                .ifPresent(polluteSignEnum -> cargoInfoDto.setPolluteSign(polluteSignEnum.getCode()));
        //附件信息
        Optional.ofNullable(cargo.getAttachments()).ifPresent(attachmentInfos -> {
            //遍历附件
            List<AttachmentInfoDto> attachmentInfoDtos = attachmentInfos.stream().map(attachmentInfo -> {
                AttachmentInfoDto attachmentInfoDto = new AttachmentInfoDto();
                attachmentInfoDto.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                attachmentInfoDto.setAttachmentName(attachmentInfo.getAttachmentName());
                attachmentInfoDto.setAttachmentType(attachmentInfo.getAttachmentType());
                attachmentInfoDto.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                attachmentInfoDto.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                attachmentInfoDto.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                return attachmentInfoDto;
            }).collect(Collectors.toList());
            //附件集
            cargoInfoDto.setCargoAttachmentInfos(attachmentInfoDtos);
        });
        //扩展属性
        cargoInfoDto.setExtendProps(cargo.getExtendProps());
        //默认使用原单的货品标识，数据同步若有特殊识别逻辑，单独替换值
        cargoInfoDto.setCargoSign(cargo.getCargoSign());
    }


    /**
     * @Description 设置配送信息
     * <AUTHOR>
     * @Date 21:53 2021/5/7
     * @Param wayBillResponse
     * @Param facadeRequest
     * @Return void
     * @Throws
     **/
    private void buildShipmentInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, ExpressOrderModel snapShotModel) {
        ShipmentInfoDto shipmentInfoDto = new ShipmentInfoDto();

        shipmentInfoDto.setEndStationNo(wayBillQueryResponse.getSiteId() != null ? String.valueOf(wayBillQueryResponse.getSiteId()) : null);//目的站点编码
        shipmentInfoDto.setEndStationName(wayBillQueryResponse.getSiteName());//目的站点名称
        shipmentInfoDto.setEndStationType(StringUtils.isNotEmpty(wayBillQueryResponse.getSiteType()) ? Integer.valueOf(wayBillQueryResponse.getSiteType()) : null);//预分拣类型
        shipmentInfoDto.setStartStationNo(wayBillQueryResponse.getPickupSiteId() != null ? String.valueOf(wayBillQueryResponse.getPickupSiteId()) : null);//取件站点
        shipmentInfoDto.setStartStationName(wayBillQueryResponse.getPickupSiteName());//取件站点名字

        if (wayBillQueryResponse.getPickUpStartTime() != null) {
            shipmentInfoDto.setExpectPickupStartTime(wayBillQueryResponse.getPickUpStartTime());//预约开始取件时间
        } else {
            clearFileds(ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME.getCode(), expressOrderModelCreator);//清空字段
        }

        if (wayBillQueryResponse.getPickUpEndTime() != null) {
            shipmentInfoDto.setExpectPickupEndTime(wayBillQueryResponse.getPickUpEndTime());//预约结束取件时间
        } else {
            clearFileds(ModifyItemConfigEnum.EXPECT_PICKUP_END_TIME.getCode(), expressOrderModelCreator);//清空字段
        }

        if (wayBillQueryResponse.getDeliveryPromiseTime() != null) {
            shipmentInfoDto.setPlanDeliveryTime(wayBillQueryResponse.getDeliveryPromiseTime());//预计送达时间
        } else {
            clearFileds(ModifyItemConfigEnum.PLAN_DELIVERY_TIME.getCode(), expressOrderModelCreator);//清空字段
        }

        if (wayBillQueryResponse.getEstimateReceiveTime() != null) {
            shipmentInfoDto.setPlanReceiveTime(wayBillQueryResponse.getEstimateReceiveTime());//预计接单时间
        } else {
            clearFileds(ModifyItemConfigEnum.PLAN_RECEIVE_TIME.getCode(), expressOrderModelCreator);//清空字段
        }

        if (wayBillQueryResponse.getShipmentStartTime() != null) {
            shipmentInfoDto.setExpectDeliveryStartTime(wayBillQueryResponse.getShipmentStartTime());//配送开始时间
        } else {
            clearFileds(ModifyItemConfigEnum.EXPECT_DELIVERY_START_TIME.getCode(), expressOrderModelCreator);//清空字段
        }
        if (wayBillQueryResponse.getShipmentEndTime() != null) {
            shipmentInfoDto.setExpectDeliveryEndTime(wayBillQueryResponse.getShipmentEndTime());//配送结束时间
        } else {
            clearFileds(ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME.getCode(), expressOrderModelCreator);//清空字段
        }
        MarkUtil markUtil = new MarkUtil(wayBillQueryResponse.getOrderMark());

        if ("3".equals(String.valueOf(markUtil.charAt(84)))) {
            shipmentInfoDto.setTransportType(TransportTypeEnum.AIRLINE);//航空
        } else if ("2".equals(String.valueOf(markUtil.charAt(84)))) {
            shipmentInfoDto.setTransportType(TransportTypeEnum.HIGH_RAILWAY);//高铁
        } else if ("1".equals(String.valueOf(markUtil.charAt(84)))) {
            shipmentInfoDto.setTransportType(TransportTypeEnum.HIGHWAY);//陆运
        } else {
            clearFileds(ModifyItemConfigEnum.TRANSPORT_TYPE.getCode(), expressOrderModelCreator);//添加清空字段
        }

        //shipmentInfoDto.setEndStationNo(wayBillQueryResponse.getSiteType());//派送站点
        shipmentInfoDto.setPickupCode(wayBillQueryResponse.getSendCode());//寄件码
        //orderMark  特殊签收方式 C、指定地点存放 B、小区门口自取 A、门卫代收 9、物业代收
        if ("9".equals(String.valueOf(markUtil.charAt(33)))) {
            shipmentInfoDto.setContactlessType(ContactlessTypeEnum.WUYEDAISHOU);
        } else if ("A".equals(String.valueOf(markUtil.charAt(33)))) {
            shipmentInfoDto.setContactlessType(ContactlessTypeEnum.MENWEIDAISHOU);
        } else if ("B".equals(String.valueOf(markUtil.charAt(33)))) {
            shipmentInfoDto.setContactlessType(ContactlessTypeEnum.XIAOQUMENKOUZIQU);
        } else if ("C".equals(String.valueOf(markUtil.charAt(33)))) {
            shipmentInfoDto.setContactlessType(ContactlessTypeEnum.ZHIDINGDIDIANCUNFANG);
        } else {
            clearFileds(ModifyItemConfigEnum.CONTACTLESS_TYPE.getCode(), expressOrderModelCreator);//添加清空字段
        }

        if(BusinessUnitEnum.CN_JDL_CC_B2C.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit())
            || BusinessUnitEnum.CN_JDL_CC_B2B.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit())){
            MedicalWarmLayerEnum medicalWarmLayerEnum = MedicalWarmLayerEnum.fromBillingCode(String.valueOf(markUtil.charAt(43)));
            if(medicalWarmLayerEnum != null){
                shipmentInfoDto.setWarmLayer(WarmLayerEnum.fromCode(medicalWarmLayerEnum.getCode()));
            } else {
                WarmLayerEnum warmLayerEnum = WarmLayerEnum.fromBillingCode(String.valueOf(markUtil.charAt(10)));
                if(warmLayerEnum != null){
                    shipmentInfoDto.setWarmLayer(warmLayerEnum);
                }else{
                    //43、10都没有的情况下清除温层
                    clearFileds(ModifyItemConfigEnum.WARM_LAYER.getCode(), expressOrderModelCreator);//添加清空字段
                }
            }
        } else {
            //温层
            WarmLayerEnum warmLayerEnum = WarmLayerEnum.fromBillingCode(String.valueOf(markUtil.charAt(10)));
            if (warmLayerEnum != null) {
                boolean isShengxian = false;
                for (ProductInfoDto productInfoDto : expressOrderModelCreator.getProducts()) {
                    if (ProductEnum.SXTH.getCode().equals(productInfoDto.getProductNo())
                            || ProductEnum.SXTK.getCode().equals(productInfoDto.getProductNo())
                            || ProductEnum.SXZS.getCode().equals(productInfoDto.getProductNo())) {
                        isShengxian = true;
                        break;
                    }
                }
                if (isShengxian) {
                    shipmentInfoDto.setWarmLayer(warmLayerEnum);
                } else {
                    clearFileds(ModifyItemConfigEnum.WARM_LAYER.getCode(), expressOrderModelCreator);//添加清空字段
                }
            } else {
                clearFileds(ModifyItemConfigEnum.WARM_LAYER.getCode(), expressOrderModelCreator);//添加清空字段
            }
        }

        //揽收方式：71位 ：1、上门揽收 2、自送网点 3、司机现场下单揽收 5、直送分拣中心
        if ('1' == markUtil.charAt(71)) {
            shipmentInfoDto.setPickupType(PickupTypeEnum.ON_SITE_PICK);
        } else if ('2' == markUtil.charAt(71)) {
            shipmentInfoDto.setPickupType(PickupTypeEnum.SELF_DELIVERY);
        } else if ('3' == markUtil.charAt(71)) {
            shipmentInfoDto.setPickupType(PickupTypeEnum.ON_SITE_PICK_CREATE_ORDER);
        } else if ('5' == markUtil.charAt(71)) {
            shipmentInfoDto.setPickupType(PickupTypeEnum.SELF_DELIVERY_DMS);
        } else if ('0' == markUtil.charAt(71)) {
            // TODO 目前只有冷链用，字段为标准字段，后续快快有诉求可以放开
            if (snapShotModel.isCCB2B()) {
                shipmentInfoDto.setPickupType(PickupTypeEnum.NO_PICKUP);
            }
        }

        //派送方式：79位 ：1、送货上门 2、自提
        if ("1".equals(String.valueOf(markUtil.charAt(79)))) {
            shipmentInfoDto.setDeliveryType(DeliveryTypeEnum.TO_DOOR);
        } else if ("2".equals(String.valueOf(markUtil.charAt(79)))) {
            shipmentInfoDto.setDeliveryType(DeliveryTypeEnum.SELF_PICKUP);
        }

        //服务要求
        Map<String, String> snapShotServiceRequirements = snapShotModel.getShipment().getServiceRequirements();
        Map<String, String> serviceRequirements = new HashMap<>();
        if (CollectionUtils.isNotEmpty(wayBillQueryResponse.getValueAddedServiceDTOList())) {
            //二手纸箱识别逻辑
            boolean selfPackage = false;
            // 特殊保障识别逻辑
            boolean specialGuaranteen = false;
            for (ValueAddedServiceDTO dto : wayBillQueryResponse.getValueAddedServiceDTOList()) {
                if (dto.getValueAddedSource() == null) {
                    continue;
                }
                if (dto.getValueAddedSource() != null && VALUE_ADDED_SOURCE_NEW_CUSTOM == dto.getValueAddedSource().intValue() && SELF_PACKAGE_KEY.equals(dto.getType())) {
                    selfPackage = true;
                } else if (VALUE_ADDED_SOURCE_NEW_CUSTOM == dto.getValueAddedSource() && SPECIAL_GUARANTEE.equals(dto.getType())) {
                    specialGuaranteen = true;
                    String value = getSpecialGuarantee(dto.getOtherParams());
                    serviceRequirements.put(ServiceRequirementsEnum.SPECIAL_GUARANTEE.getCode(), value);
                }
            }

            if (selfPackage) {
                serviceRequirements.put(ServiceRequirementsEnum.SELF_PROVIDED_PACKAGE_BOX.getCode(), OrderConstants.YES_VAL);
            } else {
                if (snapShotServiceRequirements != null
                        && snapShotServiceRequirements.get(ServiceRequirementsEnum.SELF_PROVIDED_PACKAGE_BOX.getCode()) != null) {
                    serviceRequirements.put(ServiceRequirementsEnum.SELF_PROVIDED_PACKAGE_BOX.getCode(), OrderConstants.NO_VAL);
                }
            }

            if (!specialGuaranteen) {
                // 无特殊增值服务,判断订单是否有，有则置为无
                if (null != MapUtils.getString(snapShotServiceRequirements, ServiceRequirementsEnum.SPECIAL_GUARANTEE.getCode())) {
                    serviceRequirements.put(ServiceRequirementsEnum.SPECIAL_GUARANTEE.getCode(), OrderConstants.NO_VAL);
                }
            }
        }

        if (!serviceRequirements.isEmpty()) {
            shipmentInfoDto.setServiceRequirements(serviceRequirements);
            expressOrderModelCreator.getModifiedFields().put(ModifiedFieldEnum.SERVICE_REQUIREMENTS.getCode(), ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode());
        }

        expressOrderModelCreator.setShipmentInfo(shipmentInfoDto);
    }

    /**
     * 获取特殊保障方法
     * @param otherParams
     * @return
     */
    private String getSpecialGuarantee(Map<String, String> otherParams) {
        String guaranteeType = otherParams.get(GUARANTEE_TYPE);
        if (COLD_FRESH_OPERATION.equals(guaranteeType)) {
            return OrderConstants.FRESH_GUARANTEE;
        }
        return OrderConstants.NO_VAL;
    }

    /**
     * @Description 设置财务信息
     * <AUTHOR>
     * @Date 21:53 2021/5/7
     * @Param wayBillResponse
     * @Param facadeRequest
     * @Return void
     * @Throws
     **/
    private void buildFinanceInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator
            , ExpressOrderModel snapShotModel) {
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);

        //C2B字节订单不同步财务信息
        if (snapShotModel.isC2BByteDance()) {
            LOGGER.info("C2B字节订单，不同步财务信息");
            return;
        }

        //快手小单不同步财务信息
        if (snapShotModel.isKuaiShouSmallOrder()) {
            LOGGER.info("{}快手小单，不同步财务信息",snapShotModel.orderNo());
            return;
        }

        boolean terminalEnquiryFlag = false;
        Map<String, String> channelExt = snapShotModel.getChannel().getExtendProps();
        if (MapUtils.isNotEmpty(channelExt)) {
            LOGGER.info("terminalEnquiryFlag={}", channelExt.get(AttachmentKeyEnum.TERMINAL_ENQUIRY_FLAG.getKey()));
            if (OrderConstants.YES_VAL.equals(channelExt.get(AttachmentKeyEnum.TERMINAL_ENQUIRY_FLAG.getKey()))) {
                terminalEnquiryFlag = true;
            }
        }

        MarkUtil markUtil = new MarkUtil(wayBillQueryResponse.getOrderMark());

        //0、默认，正常收取B端商家运费（寄付月结）2、到付运费（收方）3、寄付运费 5、到付（转）月结
        if ("0".equals(String.valueOf(markUtil.charAt(25)))) {
            financeInfoDto.setSettlementType(SettlementTypeEnum.MONTHLY_PAYMENT);
        } else if ("5".equals(String.valueOf(markUtil.charAt(25)))) {
            //到付转月结
            financeInfoDto.setSettlementType(SettlementTypeEnum.MONTHLY_PAYMENT_DELIVERY);
        } else if ("2".equals(String.valueOf(markUtil.charAt(25)))) {
            financeInfoDto.setSettlementType(SettlementTypeEnum.CASH_ON_DELIVERY);
        } else if ("3".equals(String.valueOf(markUtil.charAt(25)))) {
            financeInfoDto.setSettlementType(SettlementTypeEnum.CASH_ON_PICK);
        }

        //寄付月结或到付月结改为非月结，清空结算编码
        if (snapShotModel.isPickupOrDeliveryMonthlyPayment()
                && SettlementTypeEnum.CASH_ON_PICK == financeInfoDto.getSettlementType() ||
                SettlementTypeEnum.CASH_ON_DELIVERY == financeInfoDto.getSettlementType()) {
            financeInfoDto.setSettlementAccountNo("");//结算编码清空
        }

        //非月结改为月结,补齐结算账号
        if (snapShotModel.isCashOnPickOrDelivery()
                && SettlementTypeEnum.MONTHLY_PAYMENT == financeInfoDto.getSettlementType() ||
                SettlementTypeEnum.MONTHLY_PAYMENT_DELIVERY == financeInfoDto.getSettlementType()) {
            if (StringUtils.isNotBlank(wayBillQueryResponse.getSettlementCode())) {
                financeInfoDto.setSettlementAccountNo(wayBillQueryResponse.getSettlementCode());//结算编码
            } else {
                if (snapShotModel.isFreight()) {
                    if (wayBillQueryResponse.getExtendMessageDTO() != null) {
                        financeInfoDto.setSettlementAccountNo(wayBillQueryResponse.getExtendMessageDTO().getSettlementBusinessUnitCode());
                    }
                } else {
                    //如果外单履约账号为空，则取订单中心的青龙账号为结算账号
                    financeInfoDto.setSettlementAccountNo(snapShotModel.getCustomer().getAccountNo());//结算编码
                }
            }
        }

        if (OrderTypeEnum.RETURN_ORDER.getCode().equals(snapShotModel.getOrderType().getCode()) || OrderTypeEnum.READDRESS.getCode().equals(snapShotModel.getOrderType().getCode())) {
            LOGGER.info("运单号{}，订单类型为{},财务信息只同步结算方式，不同步其他财务信息", wayBillQueryResponse.getDeliveryId(), snapShotModel.getOrderType());
            return;
        }

        if (wayBillQueryResponse.getFreightPre() != null) {
            MoneyInfoDto estimatedCost = new MoneyInfoDto();
            estimatedCost.setCurrencyCode(CurrencyCodeEnum.CNY);
            estimatedCost.setAmount(wayBillQueryResponse.getFreightPre());
            financeInfoDto.setEstimateAmount(estimatedCost);//预估运费
        }

        if (terminalEnquiryFlag && BatrixSwitch.applyByBoolean(BatrixSwitchKey.TERMINAL_ENQUIRY_SKIP_ENQUIRY_INFO)) {
            LOGGER.info("终端询价，开关打开，跳过计费重体同步");
        } else {
            if (StringUtils.isNotEmpty(wayBillQueryResponse.getFeeWeight())) {
                WeightInfoDto chargedWeight = new WeightInfoDto();
                chargedWeight.setValue(TypeConversion.stringToBigDecimal(wayBillQueryResponse.getFeeWeight(), 4, null));
                chargedWeight.setUnit(WeightTypeEnum.KG);
                financeInfoDto.setBillingWeight(chargedWeight);//计费重量
            }
            if (wayBillQueryResponse.getVolume() != null) {
                VolumeInfoDto chargedVolume = new VolumeInfoDto();
                chargedVolume.setValue(TypeConversion.stringToBigDecimal(wayBillQueryResponse.getVolume().toString(), 4, null));
                chargedVolume.setUnit(VolumeTypeEnum.CM3);
                financeInfoDto.setBillingVolume(chargedVolume);//计费体积
            }
        }

        if (wayBillQueryResponse.getSumMoney() != null) {
            MoneyInfoDto discountAfterAmount = new MoneyInfoDto();
            discountAfterAmount.setAmount(TypeConversion.stringToBigDecimal(wayBillQueryResponse.getSumMoney().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
            discountAfterAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeInfoDto.setDiscountAmount(discountAfterAmount);//折后金额-实收金额
        }
        financeInfoDto.setPayDeadline(wayBillQueryResponse.getExpiredTime());//失效时间
        if (wayBillQueryResponse.getTotalPreFee() != null) {
            MoneyInfoDto discountPreAmount = new MoneyInfoDto();
            discountPreAmount.setAmount(wayBillQueryResponse.getTotalPreFee());
            discountPreAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeInfoDto.setPreAmount(discountPreAmount);//折扣前金额-折扣前总金额
        }
        //机构信息
        if (wayBillQueryResponse.getOrgId() != null) {
            financeInfoDto.setCollectionOrgNo(String.valueOf(wayBillQueryResponse.getOrgId()));//机构编码
        }

        //订单主产品和增值产品
        List<ProductInfoDto> productInfoDtoList = expressOrderModelCreator.getProducts();
        Map<String, ProductInfoDto> productInfoDtoMap = productInfoDtoList.stream().collect(Collectors.toMap(ProductInfoDto::getProductNo, Function.identity(), (t1, t2) -> t2));
        ProductInfoDto mainProductInfoDto = new ProductInfoDto();
        for (ProductInfoDto productInfoDto : productInfoDtoList) {
            if (productInfoDto.getProductType().equals(ServiceProductTypeEnum.MAIN_PRODUCT.getCode())) {
                //主产品
                mainProductInfoDto = productInfoDto;
            }
        }
        //费用明细逻辑处理
        if (CollectionUtils.isNotEmpty(wayBillQueryResponse.getWayBillDetailResponseList())) {
            if ((BusinessUnitEnum.CN_JDL_B2C.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit()) && !UnitedB2CUtil.isUnitedFreightB2C(snapShotModel))
                    || BusinessUnitEnum.CN_JDL_O2O_B.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit())
                    || BusinessUnitEnum.CN_JDL_C2B.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit())){
                financeInfoDto.setFinanceDetailInfos(b2cBuildFinanceDetailInfo(wayBillQueryResponse,mainProductInfoDto,productInfoDtoMap, expressOrderModelCreator.getBusinessIdentity().getBusinessUnit()));
            }else if(BusinessUnitEnum.CN_JDL_CC_B2C.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit())){
                financeInfoDto.setFinanceDetailInfos(ccB2cBuildFinanceDetailInfo(wayBillQueryResponse,mainProductInfoDto,productInfoDtoMap));
            } else if (BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit())
                    || BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit())
                    || UnitedB2CUtil.isUnitedFreightB2C(snapShotModel)) {
                financeInfoDto.setFinanceDetailInfos(freightBuildFinanceDetailInfo(wayBillQueryResponse, mainProductInfoDto, snapShotModel, terminalEnquiryFlag));
            } else if(BusinessUnitEnum.CN_JDL_CC_B2B.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit())){
                financeInfoDto.setFinanceDetailInfos(ccB2bBuildFinanceDetailInfo(wayBillQueryResponse,mainProductInfoDto,productInfoDtoMap));
            } else {
                financeInfoDto.setFinanceDetailInfos(c2cBuildFinanceDetailInfo(wayBillQueryResponse, expressOrderModelCreator, terminalEnquiryFlag));
            }
        }

        boolean syncPaymentType = true;
        if (terminalEnquiryFlag
                && (BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_C2C.getCode().equals(expressOrderModelCreator.getBusinessIdentity().getBusinessUnit()))) {
            if (!BatrixSwitch.applyByBoolean(BatrixSwitchKey.C2C_PAYMENT_TYPE_SYNC_SWITCH)) {
                LOGGER.info("C2C支付方式数据同步开关-关闭，不同步支付方式");
                syncPaymentType = false;
            }
        }
        if (syncPaymentType) {
            //支付方式
            //代扣模式 1:微信代扣 2:白条代扣 3:在线支付 4:预付款现结 8:字节支付 9:平台代扣
            if ("1".equals(String.valueOf(markUtil.charAt(117)))) {
                financeInfoDto.setPayment(PaymentTypeEnum.WECHAT_WITHHOLDING);
            } else if ("2".equals(String.valueOf(markUtil.charAt(117)))) {
                financeInfoDto.setPayment(PaymentTypeEnum.BAITIAO_WITHHOLDING);
            } else if ("3".equals(String.valueOf(markUtil.charAt(117)))
                    || "A".equals(String.valueOf(markUtil.charAt(117)))) {
                if ("3".equals(String.valueOf(markUtil.charAt(117)))) {
                    //先揽后付
                    financeInfoDto.setPayment(PaymentTypeEnum.PICKUP_BEFORE_PAY);
                } else if ("A".equals(String.valueOf(markUtil.charAt(117)))) {
                    //先付后揽
                    financeInfoDto.setPayment(PaymentTypeEnum.PAY_BEFORE_PICKUP);
                }

                //支付完成状态禁止更新为非支付状态校验
                Finance snapShotFinance = snapShotModel.getFinance();
                if (snapShotFinance != null) {
                    if (snapShotFinance.getPaymentStatus() == null
                            || snapShotFinance.getPaymentStatus() == PaymentStatusEnum.NO_PAYMENT) {
                        financeInfoDto.setPaymentStatus(PaymentStatusEnum.WAITING_FOR_PAYMENT);
                    }
                }
                //支付成功
                if (wayBillQueryResponse.getPayStatus() != null && wayBillQueryResponse.getPayStatus() == 0) {
                    financeInfoDto.setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT);
                }
            } else if ("4".equals(String.valueOf(markUtil.charAt(117)))) {
                //4-预付款现结  对应订单中心枚举的 6-预收款抵扣
                financeInfoDto.setPayment(PaymentTypeEnum.DEDUCTION_OF_ADVANCE_PAYMENT);
            } else if ("8".equals(String.valueOf(markUtil.charAt(117)))) {
                financeInfoDto.setPayment(PaymentTypeEnum.BYTEDANCE_PAY);
            } else if ("9".equals(String.valueOf(markUtil.charAt(117)))) {
                financeInfoDto.setPayment(PaymentTypeEnum.PLATFORM_WITHHOLDING);
            } else if ('B' == markUtil.charAt(117)) {
                financeInfoDto.setPayment(PaymentTypeEnum.UEP_WITHHOLDING);
            } else if ('C' == markUtil.charAt(117)) {
                financeInfoDto.setPayment(PaymentTypeEnum.PAY_FIRST);
            } else if ('D' == markUtil.charAt(117)) {
                financeInfoDto.setPayment(PaymentTypeEnum.PLATFORM_PARTIAL_WITHHOLDING);
            } else if ('E' == markUtil.charAt(117)) {
                financeInfoDto.setPayment(PaymentTypeEnum.ALIPAY_WITHHOLDING);
            } else if ('F' == markUtil.charAt(117)) {
                financeInfoDto.setPayment(PaymentTypeEnum.B2C_PARTIAL_WITHHOLDING);
            } else {
                clearFileds(ModifyItemConfigEnum.PAYMENT.getCode(), expressOrderModelCreator);//清空字段
            }
        } else {
            LOGGER.info("不同步支付方式");
        }


        //改址在线支付状态 --
        /*if (OrderTypeEnum.READDRESS == expressOrderModelCreator.getOrderType() && !"0".equals(String.valueOf(markUtil.charAt(115)))) {
            if ("1".equals(String.valueOf(markUtil.charAt(115)))) {
                financeInfoDto.setPaymentStatus(PaymentStatusEnum.COMPLETEPAYMENT);
            } else if ("2".equals(String.valueOf(markUtil.charAt(115)))) {
                financeInfoDto.setPaymentStatus(PaymentStatusEnum.WAITINGFORPAYMENT);
            } else if ("3".equals(String.valueOf(markUtil.charAt(115)))) {
                financeInfoDto.setPaymentStatus(PaymentStatusEnum.WAITINGFORPAYMENT);
                expressOrderModelCreator.setInitiatorType(InitiatorTypeEnum.CONSIGNOR);
            } else if ("4".equals(String.valueOf(markUtil.charAt(115)))) {
                financeInfoDto.setPaymentStatus(PaymentStatusEnum.WAITINGFORPAYMENT);
                expressOrderModelCreator.setInitiatorType(InitiatorTypeEnum.CONSIGNEE);
            }
        }*/
        // 积分信息
        financeInfoDto.setPointsInfoDto(toPointsInfo(wayBillQueryResponse.getIntegralNumber(), wayBillQueryResponse.getIntegralDeductAmount()));//积分
        //附加费
        if (!CollectionUtils.isEmpty(wayBillQueryResponse.getValueAddedServiceDTOList())) {
            List<CostInfoDto> attachFees = new ArrayList<>();
            for (ValueAddedServiceDTO dto : wayBillQueryResponse.getValueAddedServiceDTOList()) {
                if (null == dto) {
                    continue;
                }
                //
                if (AttachFeeEnum.of(dto.getType()) != null) {
                    CostInfoDto costInfoDto = new CostInfoDto();
                    costInfoDto.setCostNo(dto.getType());
                    costInfoDto.setExtendProps(dto.getOtherParams());
                    attachFees.add(costInfoDto);
                }
            }
            if (CollectionUtils.isNotEmpty(attachFees)) {
                financeInfoDto.setAttachFees(attachFees);
            }
        }
    }

    private List<FinanceDetailInfoDto> c2cBuildFinanceDetailInfo(WayBillQueryResponse wayBillQueryResponse,ExpressOrderModelCreator expressOrderModelCreator, boolean terminalEnquiryFlag){
        if (terminalEnquiryFlag) {
            LOGGER.info("快递C2C，terminalEnquiryFlag={}", terminalEnquiryFlag);
            if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.TERMINAL_ENQUIRY_FINANCE_DETAIL_SYNC_SWITCH)) {
                LOGGER.info("终端询价的订单，同步费用明细开关：开启，继续同步费用明细");
            } else {
                LOGGER.info("终端询价的订单，同步费用明细开关：关闭，不同步费用明细");
                return new ArrayList<>();
            }
        }
        //运单主产品
        List<WayBillDetailResponse> billMainProductList = new ArrayList<>();
        //运单子产品
        Map<String, WayBillDetailResponse> billSubProductHashMap = new HashMap<>();
        wayBillQueryResponse.getWayBillDetailResponseList().forEach(feeDetail -> {
            if (null == FeeDetailMainEnum.getFeeType(feeDetail.getFeeType())) {
                billSubProductHashMap.put(feeDetail.getFeeType(), feeDetail);
            } else {
                billMainProductList.add(feeDetail);
            }
        });
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (WayBillDetailResponse detailResultDTO : billMainProductList) {
            FinanceDetailInfoDto financeDetailInfoDto = new FinanceDetailInfoDto();
            // 费用编码
            financeDetailInfoDto.setCostNo(ofCostNo(detailResultDTO.getFeeType(), expressOrderModelCreator.getProducts(), financeDetailInfoDto));
            //设置运单费用信息到费用remark里面
            financeDetailInfoDto.setRemark(JSONUtils.beanToJSONDefault(detailResultDTO));
            if (financeDetailInfoDto.getRemark() != null && financeDetailInfoDto.getRemark().length() >= 500) {
                financeDetailInfoDto.setRemark(financeDetailInfoDto.getRemark().substring(0, 499));
            }
            // 费用名称
            financeDetailInfoDto.setCostName(detailResultDTO.getFeeTypeName());
            // 设置折前金额
            MoneyInfoDto preAmount = new MoneyInfoDto();
            preAmount.setAmount(detailResultDTO.getAmount());
            preAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeDetailInfoDto.setPreAmount(preAmount);
            // 费用折后信息 折前+sum(折扣码对应的金额)
            String[] subFeeTypes = FeeDetailMainEnum.getFeeType(detailResultDTO.getFeeType()).getSubFeeType().split(",");
            List<DiscountInfoDto> discountFacadeList = new ArrayList<>();
            BigDecimal afterDiscountAmount = preAmount.getAmount();
            for (String subFeeType : subFeeTypes) {
                WayBillDetailResponse sub = billSubProductHashMap.get(subFeeType);
                if (sub != null) {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(sub.getFeeType());
                    Money subDetailMoney = new Money();
                    subDetailMoney.setAmount(sub.getAmount());
                    subDetailMoney.setCurrency(CurrencyCodeEnum.CNY);
                    discountInfoDto.setDiscountedAmount(subDetailMoney);
                    discountFacadeList.add(discountInfoDto);
                    // 折后金额: 负值
                    afterDiscountAmount = afterDiscountAmount.add(subDetailMoney.getAmount());
                }
            }
            financeDetailInfoDto.setDiscountInfoDtos(discountFacadeList);
            // 更新折后金额
            MoneyInfoDto discountAmount = new MoneyInfoDto();
            discountAmount.setAmount(afterDiscountAmount);
            discountAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeDetailInfoDto.setDiscountAmount(discountAmount);
            //积分
            financeDetailInfoDto.setPointsInfoDto(toPointsInfo(detailResultDTO.getIntegralNumber(), detailResultDTO.getIntegralDeductAmount()));
            financeDetailInfoDtoList.add(financeDetailInfoDto);
        }
        return financeDetailInfoDtoList;
    }

    private List<FinanceDetailInfoDto> b2cBuildFinanceDetailInfo(WayBillQueryResponse wayBillQueryResponse,
                                                                 ProductInfoDto mainProductInfoDto,
                                                                 Map<String, ProductInfoDto> productInfoDtoMap,
                                                                 String businessUnit){
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (WayBillDetailResponse detailResultDTO : wayBillQueryResponse.getWayBillDetailResponseList()){
            FinanceDetailInfoDto financeDetailInfoDto = new FinanceDetailInfoDto();
            // 产品编码，费用编码，费用名称，
            setProductNo(detailResultDTO,mainProductInfoDto,financeDetailInfoDto,productInfoDtoMap,businessUnit);
            //设置运单费用信息到费用remark里面
            financeDetailInfoDto.setRemark(JSONUtils.beanToJSONDefault(detailResultDTO));
            if (financeDetailInfoDto.getRemark() != null && financeDetailInfoDto.getRemark().length() >= 500) {
                financeDetailInfoDto.setRemark(financeDetailInfoDto.getRemark().substring(0, 499));
            }
            // 设置折前金额
            MoneyInfoDto preAmount = new MoneyInfoDto();
            preAmount.setAmount(detailResultDTO.getAmount());
            preAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeDetailInfoDto.setPreAmount(preAmount);
            List<DiscountInfoDto> discountFacadeList = new ArrayList<>();
            BigDecimal afterDiscountAmount = preAmount.getAmount();//B2C折前和折后相等

            financeDetailInfoDto.setDiscountInfoDtos(discountFacadeList);
            // 更新折后金额
            MoneyInfoDto discountAmount = new MoneyInfoDto();
            discountAmount.setAmount(afterDiscountAmount);
            discountAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeDetailInfoDto.setDiscountAmount(discountAmount);
            //积分
            financeDetailInfoDto.setPointsInfoDto(toPointsInfo(detailResultDTO.getIntegralNumber(), detailResultDTO.getIntegralDeductAmount()));
            financeDetailInfoDtoList.add(financeDetailInfoDto);
        }
        return financeDetailInfoDtoList;
    }



    /**
     * @Description 设置积分
     * <AUTHOR>
     * @Date 17:19 2021/5/24
     * @Param integralNumber
     * @Param integralDeductAmount
     * @Return PointsInfoDto
     * @Throws
     **/
    private PointsInfoDto toPointsInfo(Integer integralNumber, BigDecimal integralDeductAmount) {
        if (integralDeductAmount == null && integralNumber == null) {
            return null;
        }
        PointsInfoDto pointsInfoDto = new PointsInfoDto();
        if (integralDeductAmount != null) {
            MoneyInfoDto redeemPointsAmount = new MoneyInfoDto();
            redeemPointsAmount.setAmount(integralDeductAmount);
            redeemPointsAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            pointsInfoDto.setRedeemPointsAmount(redeemPointsAmount);//积分使用金额
        }
        if (integralNumber != null) {
            QuantityInfoDto redeemPointsQuantity = new QuantityInfoDto();
            redeemPointsQuantity.setValue(new BigDecimal(integralNumber.toString()));
            pointsInfoDto.setRedeemPointsQuantity(redeemPointsQuantity);//积分使用数量
        }
        return pointsInfoDto;
    }

    /**
     * @Description 设置营销信息
     * <AUTHOR>
     * @Date 21:53 2021/5/7
     * @Param wayBillResponse
     * @Param facadeRequest
     * @Return void
     * @Throws
     **/
    //TODO 需要添加优惠券使用金额
    private void buildPromotionInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, ExpressOrderModel snapShotModel){
        //orderMark
        MarkUtil markUtil = new MarkUtil(wayBillQueryResponse.getOrderMark());
        PromotionInfoDto promotionInfoDto = new PromotionInfoDto();
        //  优惠券信息 waybillCouponDTOList
        List<TicketInfoDto> ticketInfoDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(wayBillQueryResponse.getWaybillCouponResponseList())) {
            wayBillQueryResponse.getWaybillCouponResponseList().forEach(waybillCoupon -> {
                TicketInfoDto ticketInfoDto = new TicketInfoDto();
                ticketInfoDto.setTicketNo(waybillCoupon.getCouponId());
                ticketInfoDto.setTicketCategory(waybillCoupon.getCouponStyle());
                ticketInfoDto.setTicketType(waybillCoupon.getCouponType());
                MoneyInfoDto ticketMoney = new MoneyInfoDto();
                ticketMoney.setAmount(waybillCoupon.getCouponDiscount());
                ticketMoney.setCurrencyCode(CurrencyCodeEnum.CNY);
                ticketInfoDto.setTicketDiscountAmount(ticketMoney);
                ticketInfoDto.setTicketDescription(waybillCoupon.getDescription());
                ticketInfoDto.setTicketDiscountRate(waybillCoupon.getDiscountRate());
                ticketMoney = new MoneyInfoDto();
                ticketMoney.setAmount(waybillCoupon.getDiscountUpperLimit());
                ticketMoney.setCurrencyCode(CurrencyCodeEnum.CNY);
                ticketInfoDto.setTicketDiscountUpperLimit(ticketMoney);
                ticketInfoDtoList.add(ticketInfoDto);
            });
        }
        // 折扣信息 discountCodeList
        List<DiscountInfoDto> discountInfoDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(wayBillQueryResponse.getWayBillDiscountCodeList())) {
            wayBillQueryResponse.getWayBillDiscountCodeList().forEach(waybillCoupon -> {
                if (!BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.WAYBILL_DISCOUNTS_UPDATE_BLACK_LIST, waybillCoupon.getCouponId())) {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(waybillCoupon.getCouponId());
                    discountInfoDtoList.add(discountInfoDto);
                }
            });
        }

        //todo 外单运单没有承接专业市场折扣-直接覆盖会导致专业市场折扣丢失-保留原单的专业市场折扣
        BatrixSwitch.applyDefNotExecute(BatrixSwitchKey.DATA_SYNC_SNAPSHOT_DISCOUNT_SWITCH, (a) -> {
            if (null != snapShotModel && null != snapShotModel.getPromotion() && CollectionUtils.isNotEmpty(snapShotModel.getPromotion().getDiscounts())) {
                List<Discount> collect = snapShotModel.getPromotion().getDiscounts().stream().filter(discountDetail -> {
                    return PROFESSIONAL_MARKET_DISCOUNT_TYPE.equals(discountDetail.getDiscountType());
                }).collect(Collectors.toList());
                LOGGER.info("订单本身有的专业市场折扣,orderNo:{},list:{}",snapShotModel.orderNo(),JSONUtils.beanToJSONDefault(collect));
                if (CollectionUtils.isNotEmpty(collect)) {
                    collect.forEach(discount -> {
                        DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                        discountInfoDto.setDiscountNo(discount.getDiscountNo());
                        discountInfoDto.setDiscountType(discount.getDiscountType());
                        discountInfoDto.setDiscountedAmount(discount.getDiscountedAmount());
                        discountInfoDtoList.add(discountInfoDto);
                    });
                }
            }
        });

        // 外单没有存储plus免首重的扩展字段，所以不能用外单的覆盖。需要取订单上的
        if (null != snapShotModel && null != snapShotModel.getPromotion() && CollectionUtils.isNotEmpty(snapShotModel.getPromotion().getDiscounts())) {
            List<Discount> collect = snapShotModel.getPromotion().getDiscounts().stream().filter(discountDetail -> {
                return BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.WAYBILL_DISCOUNTS_UPDATE_BLACK_LIST, discountDetail.getDiscountNo());
            }).collect(Collectors.toList());
            LOGGER.info("订单本身有的plus折扣,orderNo:{},list:{}",snapShotModel.orderNo(),JSONUtils.beanToJSONDefault(collect));
            if (CollectionUtils.isNotEmpty(collect)) {
                collect.forEach(discount -> {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discount.getDiscountNo());
                    discountInfoDto.setDiscountType(discount.getDiscountType());
                    discountInfoDto.setDiscountedAmount(discount.getDiscountedAmount());
                    discountInfoDto.setExtendProps(discount.getExtendProps());
                    discountInfoDtoList.add(discountInfoDto);
                });
            }
        }


        promotionInfoDto.setTicketInfos(ticketInfoDtoList);
        promotionInfoDto.setDiscountInfos(discountInfoDtoList);

        //毕业寄
        if (!"0".equals(String.valueOf(markUtil.charAt(98)))) {
            List<ActivityInfoDto> activityInfoDtoList = promotionInfoDto.getActivityInfos();
            if (activityInfoDtoList == null) {
                activityInfoDtoList = new ArrayList<>();
            }
            ActivityInfoDto activityInfoDto = new ActivityInfoDto();
            //GRADUATION_SEND-毕业寄
            activityInfoDto.setActivityNo("GRADUATION_SEND");
            //活动参加状态 1-已参加
            activityInfoDto.setActivityStatus(1);
            // orderMark标位：1-单独寄  2-拼团寄
            // ActivityValue活动内容：1-拼团寄拼团价格 2-单独寄拼团价格
            if ("1".equals(String.valueOf(markUtil.charAt(98)))) {
                activityInfoDto.setActivityValue("2");
            } else if ("2".equals(String.valueOf(markUtil.charAt(98)))) {
                activityInfoDto.setActivityValue("1");
            }
            activityInfoDtoList.add(activityInfoDto);
            promotionInfoDto.setActivityInfos(activityInfoDtoList);
        }
        expressOrderModelCreator.setPromotionInfo(promotionInfoDto);
    }


    /**
     * @Description 费用转换编码
     * <AUTHOR>
     * @Date 16:05 2021/5/15
     * @Param feeType
     * @Return String
     * @Throws
     **/
    private String ofCostNo(String feeType, List<ProductInfoDto> productInfoDtoList, FinanceDetailInfoDto financeDetailInfoDto) {

        ProductInfoDto mainProductInfoDto = new ProductInfoDto();
        for (ProductInfoDto productInfoDto : productInfoDtoList) {
            if (productInfoDto.getProductType().equals(ServiceProductTypeEnum.MAIN_PRODUCT.getCode())) {
                //主产品
                mainProductInfoDto = productInfoDto;
            }
        }

        if (FeeDetailMainEnum.getFeeType(feeType) != null) {
            //编码0对应的金额逻辑
            if ("0".equals(feeType)) {
                financeDetailInfoDto.setProductNo(mainProductInfoDto.getProductNo());
                if (!ProductEnum.SXTH.getCode().equals(mainProductInfoDto.getProductNo()) && !ProductEnum.SXTK.getCode().equals(mainProductInfoDto.getProductNo())) {
                    return "QIPSF";
                } else if (ProductEnum.SXTH.getCode().equals(mainProductInfoDto.getProductNo())) {
                    return "SXHDYF";
                } else if (ProductEnum.SXTK.getCode().equals(mainProductInfoDto.getProductNo())) {
                    return "SXSDYF";
                }
            }
            //编码2对应的金额逻辑
            if ("2".equals(feeType)) {
                if (!ProductEnum.SXTH.getCode().equals(mainProductInfoDto.getProductNo()) && !ProductEnum.SXTK.getCode().equals(mainProductInfoDto.getProductNo())) {
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.JZD.getCode());
                    return "QLJZD";
                }
            }
            //编码3对应的金额逻辑
            if ("3".equals(feeType)) {
                //产品编码识别，需要从增值服务里取
                ProductInfoDto bjProduct = getProductInfoByNo(AddOnProductEnum.INSURED_VALUE_TOC.getCode(), productInfoDtoList);
                if (bjProduct == null) {
                    //生鲜保价
                    bjProduct = getProductInfoByNo(AddOnProductEnum.LL_ZZ_BJ.getCode(), productInfoDtoList);
                }
                if(bjProduct == null){
                    //全额保
                    bjProduct = getProductInfoByNo(AddOnProductEnum.INSURED_VALUE_FULL.getCode(), productInfoDtoList);
                }

                if (bjProduct != null) {
                    financeDetailInfoDto.setProductNo(bjProduct.getProductNo());
                } else {
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.INSURED_VALUE_TOC.getCode());
                }

                if (!ProductEnum.SXTH.getCode().equals(mainProductInfoDto.getProductNo()) && !ProductEnum.SXTK.getCode().equals(mainProductInfoDto.getProductNo())) {
                    return "QLBJ";
                } else if (ProductEnum.SXTH.getCode().equals(mainProductInfoDto.getProductNo())) {
                    return "SXHDBJ";
                } else if (ProductEnum.SXTK.getCode().equals(mainProductInfoDto.getProductNo())) {
                    return "SXSDBJ";
                }
            }
            //编码4对应的金额逻辑
            if ("4".equals(feeType)) {
                if (!ProductEnum.SXTH.getCode().equals(mainProductInfoDto.getProductNo()) && !ProductEnum.SXTK.getCode().equals(mainProductInfoDto.getProductNo())) {
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.JDL_COD_TOC.getCode());
                    return "QLDS";
                }
            }
            //编码5对应的金额逻辑
            if ("5".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.SIGN_RETURN_TOC.getCode());
                if (ProductEnum.SXTH.getCode().equals(mainProductInfoDto.getProductNo())) {
                    return "SXHDQDFH";
                } else if (ProductEnum.SXTK.getCode().equals(mainProductInfoDto.getProductNo())) {
                    return "SXSDQDFH";
                } else {
                    return "QLQDFH";
                }
            }
            //编码7对应的金额逻辑
            if ("7".equals(feeType)) {
                return "7";
            }
            //编码10对应的金额逻辑
            if ("10".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.FEATHER_LETTER.getCode());
                return "JMXFWF";
            }
            //编码11对应的金额逻辑
            if ("11".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.J_ZHUN_DA.getCode());
                return "C2CJZD";
            }
            //编码13对应的金额逻辑
            if ("13".equals(feeType)) {
                return "KDGFQFJF";
            }
            //编码12对应的金额逻辑
            if ("12".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.READDRESS.getCode());
                return "B2CTCGZF";
            }
            //编码20对应的金额逻辑
            if ("20".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.J_ZHUN_DA.getCode());
                if (ProductEnum.SXTH.getCode().equals(mainProductInfoDto.getProductNo())) {
                    return "SXTHGFQFJF";
                } else if (ProductEnum.SXTK.getCode().equals(mainProductInfoDto.getProductNo())) {
                    return "SXTKGFQFJF";
                }
            }
            //编码21对应的金额逻辑
            if ("21".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.COOLER_BOX.getCode());
                return "CPBWXFWF";
            }
            //编码6对应金额逻辑
            if ("6".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.EXPRESS_CONSUMABLES.getCode());
                return "QLHCF";
            }
            //编码22 特安服务费用信息
            if ("22".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.TE_AN_FU_WU.getCode());
                return "TEANFW";
            }
            //14 运费保费用信息
            if ("14".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.YUN_FEI_BAO.getCode());
                return "KDYFB";
            }
            // 预约派送费用信息
            if ("15".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.BOOK_DELIVERY.getCode());
                return "KD_DSPS";
            }
            // 指定签收费用信息
            if (FeeDetailMainEnum.FEE_SIGN.getFeeType().equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.DESIGNATED_SIGN.getCode());
                return "KDZDQS";
            }
            // 惊喜送达费用信息
            if ("17".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.SURPRISE_DELIVERY.getCode());
                return "DJSPDJFW";
            }
            // 快递超长超重附加费
            if ("kkcccc".equals(feeType)) {
                financeDetailInfoDto.setProductNo(AddOnProductEnum.KD_CC_CZ_FJF.getCode());
                return "kkcccc";
            }
        }
        return null;
    }

    public ProductInfoDto getProductInfoByNo(String productNo, List<ProductInfoDto> productInfoDtoList) {
        for (ProductInfoDto productInfoDto : productInfoDtoList) {
            if (productInfoDto.getProductNo().equals(productNo)) {
                return productInfoDto;
            }
        }
        return null;
    }

    /**
     * @Description 费用转换编码
     * <AUTHOR>
     * @Date 16:05 2021/5/15
     * @Param feeType
     * @Return String
     * @Throws
     **/
    private static void setProductNo(WayBillDetailResponse detailResultDTO, ProductInfoDto mainProductInfoDto, FinanceDetailInfoDto financeDetailInfoDto, Map<String, ProductInfoDto> productInfoDtoMap, String businessUnit) {
        LOGGER.info("B2C费用编码及产品编码赋值");
        //计费编码为10801
        if ("10801".equals(detailResultDTO.getFeeType())){
            //订单上主产品为非生鲜特惠（LL-HD-M）、非生鲜特快（LL-SD-M），非生鲜专送（LL-ZS-M）按照订单上主产品赋值,费用编码QIPSF
            if(productInfoDtoMap.get(ProductEnum.SXTK.getCode()) == null
                    && productInfoDtoMap.get(ProductEnum.SXTH.getCode()) == null
                    && productInfoDtoMap.get(ProductEnum.SXZS.getCode()) == null){
                financeDetailInfoDto.setCostNo("QIPSF");
                financeDetailInfoDto.setCostName("基础配送费");
                financeDetailInfoDto.setProductNo(mainProductInfoDto.getProductNo());
            }
            //订单上主产品为生鲜专送（LL-ZS-M），赋值费用编码SXZSYF-B2C
            if (productInfoDtoMap.get(ProductEnum.SXZS.getCode()) != null){
                financeDetailInfoDto.setCostNo("SXZSYF-B2C");
                financeDetailInfoDto.setCostName("生鲜专送配送费");
                financeDetailInfoDto.setProductNo(ProductEnum.SXZS.getCode());
            }
            //订单上主产品为生鲜特惠（LL-HD-M），赋值费用编码SXHDYF
            if (productInfoDtoMap.get(ProductEnum.SXTH.getCode()) != null){
                financeDetailInfoDto.setCostNo("SXHDYF");
                financeDetailInfoDto.setCostName("生鲜特惠配送费");
                financeDetailInfoDto.setProductNo(ProductEnum.SXTH.getCode());
            }
            //订单上主产品为生鲜特快（LL-SD-M），赋值费用编码SXSDYF
            if (productInfoDtoMap.get(ProductEnum.SXTK.getCode()) != null){
                financeDetailInfoDto.setCostNo("SXSDYF");
                financeDetailInfoDto.setCostName("生鲜特快配送费");
                financeDetailInfoDto.setProductNo(ProductEnum.SXTK.getCode());
            }
        }
        //计费编码为11301
        if ("11301".equals(detailResultDTO.getFeeType())){
            //订单上增值产品为保价（ed-a-0002），
            if(productInfoDtoMap.get(AddOnProductEnum.INSURED_VALUE_TOC.getCode()) != null){
                //且主产品为非生鲜特惠（LL-HD-M）、非生鲜特快（LL-SD-M），非生鲜专送（LL-ZS-M），赋值费用编码QLBJ
                if (productInfoDtoMap.get(ProductEnum.SXTK.getCode()) == null
                        && productInfoDtoMap.get(ProductEnum.SXTH.getCode()) == null
                        && productInfoDtoMap.get(ProductEnum.SXZS.getCode()) == null){
                    financeDetailInfoDto.setCostNo("QLBJ");
                    financeDetailInfoDto.setCostName("保价费");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.INSURED_VALUE_TOC.getCode());
                }
                //且主产品为生鲜专送（LL-ZS-M），赋值费用编码SXZSBJ-B2C
                if (productInfoDtoMap.get(ProductEnum.SXZS.getCode()) != null){
                    financeDetailInfoDto.setCostNo("SXZSBJ-B2C");
                    financeDetailInfoDto.setCostName("生鲜专送保价");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.INSURED_VALUE_TOC.getCode());
                }
            }
            //订单上增值产品为生鲜保价（LL-ZZ-BJ）
            if (productInfoDtoMap.get(AddOnProductEnum.LL_ZZ_BJ.getCode()) != null){
                //且主产品为生鲜特惠（LL-HD-M），赋值费用编码SXHDBJ
                if (productInfoDtoMap.get(ProductEnum.SXTH.getCode()) != null){
                    financeDetailInfoDto.setCostNo("SXHDBJ");
                    financeDetailInfoDto.setCostName("生鲜特惠保价费");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.LL_ZZ_BJ.getCode());
                }
                //且主产品为生鲜特快（LL-SD-M），赋值费用编码SXSDBJ
                if (productInfoDtoMap.get(ProductEnum.SXTK.getCode()) != null){
                    financeDetailInfoDto.setCostNo("SXSDBJ");
                    financeDetailInfoDto.setCostName("生鲜特快保价费");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.LL_ZZ_BJ.getCode());
                }
            }

        }

        //计费编码为11101
        if("11101".equals(detailResultDTO.getFeeType())){
            //订单上增值产品为代收货款（ed-a-0009），且主产品为非生鲜特惠（LL-HD-M）、非生鲜特快（LL-SD-M），赋值费用编码
            if (productInfoDtoMap.get(AddOnProductEnum.JDL_COD_TOC.getCode()) != null
                    && productInfoDtoMap.get(ProductEnum.SXTH.getCode()) == null
                    && productInfoDtoMap.get(ProductEnum.SXTK.getCode()) == null){
                financeDetailInfoDto.setCostNo("QLDS");
                financeDetailInfoDto.setCostName("代收货款费");
                financeDetailInfoDto.setProductNo(AddOnProductEnum.JDL_COD_TOC.getCode());
            }
            //订单上增值产品为生鲜代收货款（LL-ZZ-DSHK），
            if (productInfoDtoMap.get(AddOnProductEnum.LL_ZZ_DSHK.getCode()) != null){
                //且主产品为生鲜特惠（LL-HD-M），赋值费用编码SXHDDSHK
                if (productInfoDtoMap.get(ProductEnum.SXTH.getCode()) != null){
                    financeDetailInfoDto.setCostNo("SXHDDSHK");
                    financeDetailInfoDto.setCostName("生鲜特惠代收货款");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.LL_ZZ_DSHK.getCode());
                }
                //且主产品为生鲜特快（LL-SD-M），赋值费用编码SXSDDSHK
                if (productInfoDtoMap.get(ProductEnum.SXTK.getCode()) != null){
                    financeDetailInfoDto.setCostNo("SXSDDSHK");
                    financeDetailInfoDto.setCostName("生鲜特快代收货款");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.LL_ZZ_DSHK.getCode());
                }
            }
        }

        //计费编码为123
        if("123".equals(detailResultDTO.getFeeType())) {
            //订单上增值产品为签单返还（ed-a-0010），且主产品为非生鲜特惠（LL-HD-M）、非生鲜特快（LL-SD-M）
            if (productInfoDtoMap.get(AddOnProductEnum.SIGN_RETURN_TOC.getCode()) != null) {
                //且主产品为非生鲜特惠（LL-HD-M）、非生鲜特快（LL-SD-M），赋值费用编码QLQDFH
                if (productInfoDtoMap.get(ProductEnum.SXTK.getCode()) == null
                        && productInfoDtoMap.get(ProductEnum.SXTH.getCode()) == null) {
                    financeDetailInfoDto.setCostNo("QLQDFH");
                    financeDetailInfoDto.setCostName("签单返还费");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.SIGN_RETURN_TOC.getCode());
                }
            }
            //订单上增值产品为生鲜签单返还（LL-ZZ-QDFH），
            if (productInfoDtoMap.get(AddOnProductEnum.LL_ZZ_QDFH.getCode()) != null) {
                //且主产品为生鲜特惠（LL-HD-M），赋值费用编码SXHDQDFH
                if (productInfoDtoMap.get(ProductEnum.SXTH.getCode()) != null) {
                    financeDetailInfoDto.setCostNo("SXHDQDFH");
                    financeDetailInfoDto.setCostName("生鲜特惠签单返还服务费");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.LL_ZZ_QDFH.getCode());
                }
                //且主产品为生鲜特快（LL-SD-M），赋值费用编码SXSDQDFH
                if (productInfoDtoMap.get(ProductEnum.SXTK.getCode()) != null) {
                    financeDetailInfoDto.setCostNo("SXSDQDFH");
                    financeDetailInfoDto.setCostName("生鲜特快签单返还服务费");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.LL_ZZ_QDFH.getCode());
                }
            }
        }

        //计费编码为134
        if ("134".equals(detailResultDTO.getFeeType())){
            //订单上增值产品为京尊达（ed-a-0008），
            if (productInfoDtoMap.get(AddOnProductEnum.JZD.getCode()) != null){
                //且主产品为非生鲜特惠（LL-HD-M）、非生鲜特快（LL-SD-M），非生鲜专送（LL-ZS-M），赋值费用编码QLJZD
                if (productInfoDtoMap.get(ProductEnum.SXTK.getCode()) == null
                        && productInfoDtoMap.get(ProductEnum.SXTH.getCode()) == null
                        && productInfoDtoMap.get(ProductEnum.SXZS.getCode()) == null){
                    financeDetailInfoDto.setCostNo("QLJZD");
                    financeDetailInfoDto.setCostName("京尊达费");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.JZD.getCode());
                }
                //订单上主产品为生鲜专送（LL-ZS-M），赋值费用编码SXZSJZD-B2C
                if (productInfoDtoMap.get(ProductEnum.SXZS.getCode()) != null){
                    financeDetailInfoDto.setCostNo("SXZSJZD-B2C");
                    financeDetailInfoDto.setCostName("生鲜专送京尊达费");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.JZD.getCode());
                }
                //订单上主产品为生鲜特惠（LL-HD-M），赋值费用编码SXHDJZD
                if (productInfoDtoMap.get(ProductEnum.SXTH.getCode()) != null){
                    financeDetailInfoDto.setCostNo("SXHDJZD");
                    financeDetailInfoDto.setCostName("生鲜特惠京尊达费");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.JZD.getCode());
                }
                //订单上主产品为生鲜特快（LL-SD-M），赋值费用编码SXSDJZD
                if (productInfoDtoMap.get(ProductEnum.SXTK.getCode()) != null){
                    financeDetailInfoDto.setCostNo("SXSDJZD");
                    financeDetailInfoDto.setCostName("生鲜特快京尊达费");
                    financeDetailInfoDto.setProductNo(AddOnProductEnum.JZD.getCode());
                }
            }
        }

        //计费编码为137
        if ("137".equals(detailResultDTO.getFeeType())){
            //识别外单费用编码为137，赋值费用编码QLHCF
            financeDetailInfoDto.setCostNo("QLHCF");
            financeDetailInfoDto.setCostName("包装耗材费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.PACKAGE_SERVICE_TOC.getCode());
        }
        //计费编码为138
        if ("138".equals(detailResultDTO.getFeeType())){
            //识别外单费用编码为137，赋值费用编码QLDZQD
            financeDetailInfoDto.setCostNo("QLDZQD");
            financeDetailInfoDto.setCostName("电子签单费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.SIGN_RETURN_TOC.getCode());
        }
        //计费编码为139
        if ("139".equals(detailResultDTO.getFeeType())){
            //识别外单费用编码为137，赋值费用编码XSZT
            financeDetailInfoDto.setCostNo("XSZT");
            financeDetailInfoDto.setCostName("协商再投-快递");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode());
        }

        if ("CPBWXFWF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("CPBWXFWF");
            financeDetailInfoDto.setCostName("保温箱服务费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.COOLER_BOX.getCode());
        }
        if ("JMXFWF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("JMXFWF");
            financeDetailInfoDto.setCostName("鸡毛信服务费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.FEATHER_LETTER.getCode());
        }
        if ("C2CJZD".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("C2CJZD");
            financeDetailInfoDto.setCostName("京准达服务费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.J_ZHUN_DA.getCode());
        }
        if ("B2CTCGZF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("B2CTCGZF");
            financeDetailInfoDto.setCostName("改址服务费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.READDRESS.getCode());
        }
        if ("MXLS".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("MXLS");
            financeDetailInfoDto.setCostName("明细揽收费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.MXLS.getCode());
        }

        if ("SXHDHCF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("SXHDHCF");
            financeDetailInfoDto.setCostName("生鲜包装服务费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.SXHDHCF.getCode());
        }
        if ("SXSDHCF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("SXSDHCF");
            financeDetailInfoDto.setCostName("生鲜包装服务费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.SXHDHCF.getCode());
        }

        if ("SXTHGFQFJF".equals(detailResultDTO.getFeeType())){
            //暂无增值服务对应
            financeDetailInfoDto.setCostNo("SXTHGFQFJF");
            financeDetailInfoDto.setCostName("生鲜特惠高峰期附加费");
        }
        if ("SXTKGFQFJF".equals(detailResultDTO.getFeeType())){
            //暂无增值服务对应
            financeDetailInfoDto.setCostNo("SXTKGFQFJF");
            financeDetailInfoDto.setCostName("生鲜特快高峰期附加费");
        }
        if ("KDGFQFJF".equals(detailResultDTO.getFeeType())){
            //暂无增值服务对应
            financeDetailInfoDto.setCostNo("KDGFQFJF");
            financeDetailInfoDto.setCostName("春节高峰期附加费");
        }
        if ("ed-a-0012c".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("ed-a-0012c");
            financeDetailInfoDto.setCostName("检查塑封包装费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.PLASTIC_SEAL_PACKING.getCode());
        }
        if ("ed-a-0013c".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("ed-a-0013c");
            financeDetailInfoDto.setCostName("检查原包装（丢失）费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.ORIGINAL_PACKING_LOSE.getCode());
        }
        if ("ed-a-0014c".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("ed-a-0014c");
            financeDetailInfoDto.setCostName("检查外包装费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.ORIGINAL_PACKING_DAMAGED.getCode());
        }

        if ("ed-a-0015c".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("ed-a-0015c");
            financeDetailInfoDto.setCostName("检查附件费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.ATTACHMENT.getCode());
        }
        if ("ed-a-0016c".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("ed-a-0016c");
            financeDetailInfoDto.setCostName("检查商品外观费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.GOODS_APPEARANCE.getCode());
        }
        if ("ed-a-0017c".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("ed-a-0017c");
            financeDetailInfoDto.setCostName("检查使用情况费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.USE_STATUS.getCode());
        }
        if ("ed-a-0018c".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("ed-a-0018c");
            financeDetailInfoDto.setCostName("检查SN费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CHECK_SN.getCode());
        }
        if ("ed-a-0020c".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("ed-a-0020c");
            financeDetailInfoDto.setCostName("防撕码收集费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.PROOF_PICKTEAR_CODE.getCode());
        }
        if ("QLQJPZ".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("QLQJPZ");
            financeDetailInfoDto.setCostName("商品拍照费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.PICKUP_PICTURE.getCode());
        }
        if ("KDYFB".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("KDYFB");
            financeDetailInfoDto.setCostName(AddOnProductEnum.YUN_FEI_BAO.getDesc());
            financeDetailInfoDto.setProductNo(AddOnProductEnum.YUN_FEI_BAO.getCode());
        }
        if ("DABAOFW".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("DABAOFW");
            financeDetailInfoDto.setCostName("打包服务");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.DA_BAO_FU_WU.getCode());
        }
        // FIXME C2B也有
        if ("TEANFW".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("TEANFW");
            financeDetailInfoDto.setCostName("特安服务");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.TE_AN_FU_WU.getCode());
        }
        if ("JZSZZ".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("JZSZZ");
            financeDetailInfoDto.setCostName("京尊送");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.JZS_FU_WU.getCode());
        }
        if ("ed-a-0045-F".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("ed-a-0045-F");
            financeDetailInfoDto.setCostName("快递入仓");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.KUAI_DI_RU_CANG.getCode());
        }
        if ("kkcccc".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("kkcccc");
            financeDetailInfoDto.setCostName("快递超长超重附加费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.KD_CC_CZ_FJF.getCode());
        }
        if("KD_YJLS".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("KD_YJLS");
            financeDetailInfoDto.setCostName("夜间揽收-快递");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.EXPRESS_NIGHT_COLLECTION.getCode());
        }

        if("KD_TZH".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("KD_TZH");
            financeDetailInfoDto.setCostName("京碳惠计费-快递");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CARBON_EMISSION_CALCULATION_EXPRESS.getCode());
        }

        if("KY_TZH".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("KY_TZH");
            financeDetailInfoDto.setCostName("京碳惠计费-快运");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CARBON_EMISSION_CALCULATION_FREIGHT.getCode());
        }

        if ("KD_DSPS".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("KD_DSPS");
            financeDetailInfoDto.setCostName("定时派送费-快递");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.BOOK_DELIVERY.getCode());
        }

        // 兜底方法，新增费用明细与合并都加到此方法上面
        if (financeDetailInfoDto.getCostNo() == null) {
            List<String> setFeeTypeIfCostNoNullBusinessUnitList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.SET_FEE_TYPE_IF_COST_NO_NULL_BUSINESS_UNIT_LIST, ",");
            if (CollectionUtils.isNotEmpty(setFeeTypeIfCostNoNullBusinessUnitList)
                    && StringUtils.isNotBlank(businessUnit)
                    && setFeeTypeIfCostNoNullBusinessUnitList.contains(businessUnit)) {
                LOGGER.info("数据同步，costNo为null，使用feeType兜底，businessUnit="+businessUnit);
                financeDetailInfoDto.setCostNo(detailResultDTO.getFeeType());
                financeDetailInfoDto.setCostName(detailResultDTO.getFeeTypeName());
                financeDetailInfoDto.setProductNo(detailResultDTO.getProductCode());
            }
        }
    }

    /**
     * @Description 清除字段
     * <AUTHOR>
     * @Date 18:12 2021/5/20
     * @Param clearFiled
     * @Param expressOrderModelCreator
     * @Return void
     * @Throws
     **/
    private void clearFileds(String clearFiled, ExpressOrderModelCreator expressOrderModelCreator) {
        HashSet<String> hashSet = new HashSet<>(expressOrderModelCreator.getClearFields());
        if (StringUtils.isNotEmpty(clearFiled)) {
            hashSet.add(clearFiled);
            expressOrderModelCreator.setClearFields(new ArrayList<>(hashSet));
        }
    }

    /**
     * @Description 设置产品扩展属性
     * <AUTHOR>
     * @Date 17:38 2021/6/22
     * @Param productInfoDto
     * @Param productFacadeMap
     * @Param productCode
     * @Return void
     * @Throws
     **/
    private void setProductAttrs(ProductInfoDto productInfoDto, Map<String, ProductFacade> productFacadeMap, String productCode) {
        if (productFacadeMap.get(productCode) != null) {
            productInfoDto.setProductAttrs(productFacadeMap.get(productCode).getProductAttrs());
            LOGGER.info("同步运单数据特殊处理增值服务编码,产品要素不做覆盖处理:productNo={},productFacadeMap={}", productCode, JSONUtils.beanToJSONDefault(productInfoDto.getProductAttrs()));
        } else {
            productInfoDto.setProductAttrs(new HashMap<>());
        }
    }

    /**
     * 将运单信息-由产品中心定义的标准增值产品的要素补充到 当前需要同步的增值服务
     * @param productInfoDto 需要同步的增值服务
     * @param valueAddedServiceDTOList 运单信息-由产品中心定义的标准增值产品
     * 2024-01-31 保价增值服务补充保价签约类型-【https://joyspace.jd.com/pages/E6W5AXKDAwIRFK8v4LHe】
     */
    private void addProductAttrsByValueAddedService(ProductInfoDto productInfoDto, List<ValueAddedServiceDTO> valueAddedServiceDTOList) {
        //后来由产品中心定义的标准增值产品都是从这个集合里取
        if (CollectionUtils.isNotEmpty(valueAddedServiceDTOList)) {
            for (ValueAddedServiceDTO dto : valueAddedServiceDTOList) {
                if(null == dto){
                    continue;
                }
                //主要获取保价的要素，后续别的增值服务使用可以考虑位置迁移，和不同增值服务的特殊处理情况
                if(productInfoDto.getProductNo().equals(dto.getType())){
                    if(MapUtils.isNotEmpty(dto.getOtherParams())){
                        productInfoDto.getProductAttrs().putAll(dto.getOtherParams());
                    }
                }
            }
        }
    }

    /**
     * 外单序列化对象
     * @copyright    &copy;2022 JDL.CN All Right Reserved
     * <AUTHOR> liu
     * @date         2022/11/21 20:58
     * @version      1.0
     * @since        1.8
     */
    @Data
    private static class AgreementInfo {
        /** 免赔模板 */
        String deductibleCode;
        /** 协议ID */
        String agreementID;
        /** 签署人 */
        String signer;
        /** 签署时间 */
        Date signingTime;
        /** 协议签署维度 */
        String signType;

        /**
         * 对象转换方法
         * @see AgreementInfoDto
         */
        AgreementInfoDto toAgreementInfoDto() {
            AgreementInfoDto agreementInfoDto = new AgreementInfoDto();
            agreementInfoDto.setAgreementType(this.deductibleCode);
            agreementInfoDto.setAgreementId(this.agreementID);
            agreementInfoDto.setSigner(this.signer);
            agreementInfoDto.setSigningTime(this.signingTime);
            if (StringUtils.isNotBlank(signType)) {
                Map<String, String> map = new HashMap<>();
                map.put("signType", signType);
                agreementInfoDto.setExtendProps(map);
            }
            return agreementInfoDto;
        }
    }

    /**
     * 判断是否是冷链B2C业务
     * @param model
     * @param wayBillQueryResponse
     * @return
     */
    private boolean isCCB2C(ExpressOrderModel model, WayBillQueryResponse wayBillQueryResponse){
        // 快运产品和快递产品不同，冷链特殊处理
        if (model.isCCB2C() || ProductEnum.CC_LLZS == ProductEnum.of(wayBillQueryResponse.getProductType())
                || ProductEnum.CC_YYLL == ProductEnum.of(wayBillQueryResponse.getProductType())
                || ProductEnum.CC_YYZS == ProductEnum.of(wayBillQueryResponse.getProductType())) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否快运
     */
    private boolean isFreight(ExpressOrderModel orderModel) {
        return BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 判断外单详情orderMark标位是否快运，根据40位判断快运标识（2-快运，3-仓配接货仓）
     *
     * @param orderMark
     * @return
     */
    private boolean isFreightByOrderMark(String orderMark) {

        if (orderMark == null) {
            return false;
        }

        MarkUtil markUtil = new MarkUtil(orderMark);
        String orderMark40 = String.valueOf(markUtil.charAt(40));
        if (FREIGHT_SIGN.equals(orderMark40)
                || WAREHOUSE_MODE_FREIGHT_SIGN.equals(orderMark40)) {
            LOGGER.info("orderMark标位是快运，orderMark40: {}", orderMark40);
            return true;
        }

        return false;
    }

    /**
     * 设置快运产品服务信息
     **/
    private void buildFreightProductInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, Map<String, ProductFacade> productFacadeMap, ExpressOrderModel snapShotModel) {
        //key=ProductNo
        Map<String, ProductInfoDto> productInfoDtoHashMap = new HashMap<>();

        // 主产品
        ProductInfoDto mainProduct = buildFreightMainProduct(wayBillQueryResponse, productFacadeMap);
        productInfoDtoHashMap.put(mainProduct.getProductNo(), mainProduct);

        // 增值产品
        List<ProductInfoDto> addedProductList = buildFreightAddedProduct(mainProduct, wayBillQueryResponse, expressOrderModelCreator, productFacadeMap);
        if (CollectionUtils.isNotEmpty(addedProductList)) {
            addedProductList.forEach(addedProduct -> {
                productInfoDtoHashMap.put(addedProduct.getProductNo(), addedProduct);
            });
        }

        expressOrderModelCreator.setProducts(new ArrayList<>(productInfoDtoHashMap.values()));
    }

    /**
     * 构建快运主产品
     */
    private ProductInfoDto buildFreightMainProduct(WayBillQueryResponse wayBillQueryResponse, Map<String, ProductFacade> productFacadeMap) {
        MarkUtil markUtil = new MarkUtil(wayBillQueryResponse.getOrderMark());
        if (!FREIGHT_SIGN.equals(String.valueOf(markUtil.charAt(40)))
                && !WAREHOUSE_MODE_FREIGHT_SIGN.equals(String.valueOf(markUtil.charAt(40)))) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("快运外单标位异常");
        }
        String productName;
        String productNo;
        if (ProductEnum.TKLD.getSign().equals(String.valueOf(markUtil.charAt(ProductEnum.TKLD.getIndex())))) {
            productName = ProductEnum.TKLD.getDesc();
            productNo = ProductEnum.TKLD.getCode();
        } else if (ProductEnum.THZH.getSign().equals(String.valueOf(markUtil.charAt(ProductEnum.THZH.getIndex())))) {
            productName = ProductEnum.THZH.getDesc();
            productNo = ProductEnum.THZH.getCode();
        } else if (ProductEnum.TKZH.getSign().equals(String.valueOf(markUtil.charAt(ProductEnum.TKZH.getIndex())))) {
            productName = ProductEnum.TKZH.getDesc();
            productNo = ProductEnum.TKZH.getCode();
        } else if (ProductEnum.KYLD.getSign().equals(String.valueOf(markUtil.charAt(ProductEnum.KYLD.getIndex())))) {
            productName = ProductEnum.KYLD.getDesc();
            productNo = ProductEnum.KYLD.getCode();
        } else if (ProductEnum.THPH.getSign().equals(String.valueOf(markUtil.charAt(ProductEnum.THPH.getIndex())))) {
            productName = ProductEnum.THPH.getDesc();
            productNo = ProductEnum.THPH.getCode();
        } else if (ProductEnum.ACSD.getSign().equals(String.valueOf(markUtil.charAt(ProductEnum.ACSD.getIndex())))) {
            productName = ProductEnum.ACSD.getDesc();
            productNo = ProductEnum.ACSD.getCode();
        } else if (ProductEnum.HKZH.getSign().equals(String.valueOf(markUtil.charAt(ProductEnum.HKZH.getIndex())))) {
            productName = ProductEnum.HKZH.getDesc();
            productNo = ProductEnum.HKZH.getCode();
        } else {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("快运外单标位异常");
        }

        ProductInfoDto mainProduct = new ProductInfoDto();
        setProductAttrs(mainProduct, productFacadeMap, mainProduct.getProductNo());
        mainProduct.setProductType(ServiceProductTypeEnum.MAIN_PRODUCT.getCode());
        mainProduct.setProductName(productName);
        mainProduct.setProductNo(productNo);
        return mainProduct;
    }

    /**
     * 从查询结果的增值服务构建增值产品
     * <p>与快递类似，参考buildProductInfo方法</p>
     * {@link cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.WaybillInfoMappingUtil#buildProductInfo}
     */
    private List<ProductInfoDto> buildFreightAddedProduct(ProductInfoDto mainProduct, WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, Map<String, ProductFacade> productFacadeMap) {
        List<ProductInfoDto> addedProductList = new ArrayList<>();

        if(!CollectionUtils.isEmpty(wayBillQueryResponse.getValueAddedServiceDTOList())){
            for (ValueAddedServiceDTO dto : wayBillQueryResponse.getValueAddedServiceDTOList()) {
                if (null == dto) {
                    continue;
                }
                // 主产品扩展关联产品信息
                if(expressUccConfigCenter.isSyncMainProductExtRefWhite(dto.getType())){
                    Map<String, String> extendProps = Optional.ofNullable(mainProduct.getExtendProps()).orElse(new HashMap<>());
                    extendProps.put(PRODUCT_EXT_REF_PRODUCTNO,dto.getType());
                    mainProduct.setExtendProps(extendProps);
                }

                // 免赔协议：无须通过配置中心判断，直接同步
                if (DEDUCTIBLE_SERVICE.equals(dto.getType())) {
                    Map<String, String> otherParams = dto.getOtherParams();
                    if (MapUtils.isEmpty(otherParams)) {
                        continue;
                    }
                    List<AgreementInfoDto> agreementInfoDtos = otherParams.values().stream()
                            .filter(StringUtils::isNotBlank)
                            .map(jsonStr -> JSONUtils.jsonToBean(jsonStr, WaybillInfoMappingUtil.AgreementInfo.class))
                            .filter(Objects::nonNull)
                            .map(WaybillInfoMappingUtil.AgreementInfo::toAgreementInfoDto)
                            .collect(Collectors.toList());
                    expressOrderModelCreator.setAgreementInfoDtos(agreementInfoDtos);
                }

                // 除免赔协议外的增值服务：通过配置中心判断是否同步
                // fixme 配置DUCC白名单需要同步检查枚举类是否配置对应产品 AddOnProductEnum
                if (expressUccConfigCenter.isSyncNewAddOnProductWhite(dto.getType())) {
                    // 合并签收不处理（参考快递）
                    if (isCombineSign(dto)) {
                        continue;
                    }
                    ProductInfoDto productInfoDto = new ProductInfoDto();
                    productInfoDto.setProductNo(dto.getType());
                    productInfoDto.setProductName(AddOnProductEnum.of(dto.getType()).getDesc());
                    productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                    productInfoDto.setParentNo(mainProduct.getProductNo());
                    // 快运不能直接使用 setProductAttrs(ProductInfoDto productInfoDto, Map<String, ProductFacade> productFacadeMap, String productCode)
                    if (productFacadeMap.get(dto.getType()) != null) {
                        // 产品编码在 expressUccConfigCenter.syncWaybillDataSpecialHandlerAddedProduct 中则用持久化数据，否则用运单数据覆盖。
                        productInfoDto.setProductAttrs(productFacadeMap.get(dto.getType()).getProductAttrs());
                        LOGGER.info("同步运单数据特殊处理增值服务编码,产品要素不做覆盖处理:productNo={},productFacadeMap={}", dto.getType(), JSONUtils.beanToJSONDefault(productInfoDto.getProductAttrs()));
                    } else {
                        productInfoDto.setProductAttrs(dto.getOtherParams());
                    }
                    addedProductList.add(productInfoDto);
                }
            }
        }

        // 处理签单返还的产品要素：根据标位转为产品中心格式--调整为标准增值服务同步
        //handleFreightSignReturn(mainProduct, addedProductList, wayBillQueryResponse, productFacadeMap);

        if (CollectionUtils.isEmpty(expressOrderModelCreator.getAgreementInfoDtos())) {
            expressOrderModelCreator.getModifiedFields().put(ModifiedFieldEnum.AGREEMENT_INFOS.getCode(), ModifiedFieldValueEnum.ALL_DELETE.getCode());
        }

        return addedProductList;
    }

    /**
     * 处理签单返还的产品要素：根据标位转为产品中心格式
     **/
    private void handleFreightSignReturn(ProductInfoDto mainProduct, List<ProductInfoDto> addedProductList, WayBillQueryResponse wayBillQueryResponse, Map<String, ProductFacade> productFacadeMap) {
        MarkUtil markUtil = new MarkUtil(wayBillQueryResponse.getOrderMark());

        if (!"1".equals(String.valueOf(markUtil.charAt(4)))
                && !"3".equals(String.valueOf(markUtil.charAt(4)))
                && !"4".equals(String.valueOf(markUtil.charAt(4)))
                && !"5".equals(String.valueOf(markUtil.charAt(4)))
                && !"6".equals(String.valueOf(markUtil.charAt(4)))) {
            return;
        }

        ProductInfoDto productInfoDto = new ProductInfoDto();
        setProductAttrs(productInfoDto, productFacadeMap, AddOnProductEnum.SIGN_RETURN_TOB.getCode());
        productInfoDto.setProductNo(AddOnProductEnum.SIGN_RETURN_TOB.getCode());
        productInfoDto.setProductName(AddOnProductEnum.SIGN_RETURN_TOB.getDesc());
        productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
        productInfoDto.setParentNo(mainProduct.getProductNo());

        if ("1".equals(String.valueOf(markUtil.charAt(4)))) {
            JSONArray reReceiveVal = new JSONArray();
            reReceiveVal.add("written");
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE.getCode(), reReceiveVal.toString());
        } else if ("3".equals(String.valueOf(markUtil.charAt(4)))) {
            JSONArray reReceiveVal = new JSONArray();
            reReceiveVal.add("electronic");
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE.getCode(), reReceiveVal.toString());
        } else if ("4".equals(String.valueOf(markUtil.charAt(4)))) {
            JSONArray reReceiveVal = new JSONArray();
            reReceiveVal.add("written");
            reReceiveVal.add("electronic");
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE.getCode(), reReceiveVal.toString());
        } else if ("5".equals(String.valueOf(markUtil.charAt(4)))) {
            // 链上签
            JSONArray reReceiveVal = new JSONArray();
            reReceiveVal.add("signOnline");
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE.getCode(), reReceiveVal.toString());
        } else if ("6".equals(String.valueOf(markUtil.charAt(4)))) {
            // 链上签+纸质
            JSONArray reReceiveVal = new JSONArray();
            reReceiveVal.add("written");
            reReceiveVal.add("signOnline");
            productInfoDto.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE.getCode(), reReceiveVal.toString());
        }

        addedProductList.add(productInfoDto);
    }

    /**
     * 快运构建财务信息明细
     **/
    private List<FinanceDetailInfoDto> freightBuildFinanceDetailInfo(WayBillQueryResponse wayBillQueryResponse,
                                                                     ProductInfoDto mainProductInfoDto,ExpressOrderModel snapShotModel, boolean terminalEnquiryFlag) {
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();

        if (snapShotModel.isFreightC2C() && terminalEnquiryFlag) {
            LOGGER.info("快运C2C，terminalEnquiryFlag={}", terminalEnquiryFlag);
            if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.TERMINAL_ENQUIRY_FINANCE_DETAIL_SYNC_SWITCH)) {
                LOGGER.info("终端询价的订单，同步费用明细开关：开启，继续同步费用明细");
            } else {
                LOGGER.info("终端询价的订单，同步费用明细开关：关闭，不同步费用明细");
                return financeDetailInfoDtoList;
            }
        }

        for (WayBillDetailResponse detailResultDTO : wayBillQueryResponse.getWayBillDetailResponseList()){
            FinanceDetailInfoDto financeDetailInfoDto = new FinanceDetailInfoDto();
            // 产品编码，费用编码，费用名称
            freightSetProductNo(detailResultDTO,mainProductInfoDto,financeDetailInfoDto);
            //设置运单费用信息到费用remark里面
            financeDetailInfoDto.setRemark(JSONUtils.beanToJSONDefault(detailResultDTO));
            if (financeDetailInfoDto.getRemark() != null && financeDetailInfoDto.getRemark().length() >= 500) {
                financeDetailInfoDto.setRemark(financeDetailInfoDto.getRemark().substring(0, 499));
            }
            // 设置折前金额
            MoneyInfoDto preAmount = new MoneyInfoDto();
            preAmount.setAmount(detailResultDTO.getPreFee());
            preAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeDetailInfoDto.setPreAmount(preAmount);
            /*List<DiscountInfoDto> discountFacadeList = new ArrayList<>();
            BigDecimal afterDiscountAmount = preAmount.getAmount();

            financeDetailInfoDto.setDiscountInfoDtos(discountFacadeList);*/
            // 更新折后金额
            MoneyInfoDto discountAmount = new MoneyInfoDto();
            discountAmount.setAmount(detailResultDTO.getAmount());
            discountAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeDetailInfoDto.setDiscountAmount(discountAmount);
            //积分
            financeDetailInfoDto.setPointsInfoDto(toPointsInfo(detailResultDTO.getIntegralNumber(), detailResultDTO.getIntegralDeductAmount()));
            financeDetailInfoDtoList.add(financeDetailInfoDto);
        }
        return financeDetailInfoDtoList;
    }

    /**
     * 根据外单的计费编码补充产品编码，费用编码，费用名称
     **/
    private void freightSetProductNo(WayBillDetailResponse detailResultDTO, ProductInfoDto mainProductInfoDto, FinanceDetailInfoDto financeDetailInfoDto) {
        // 快运 feeType 与 costNo 一致，直接赋值
        financeDetailInfoDto.setCostNo(detailResultDTO.getFeeType());
        financeDetailInfoDto.setCostName(detailResultDTO.getFeeTypeName());
        financeDetailInfoDto.setProductNo(detailResultDTO.getProductCode());
        financeDetailInfoDto.setProductName(detailResultDTO.getProductName());
    }

    /**
     * 设置冷链B2C产品服务信息
     **/
    private void buildCCB2CProductInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, Map<String, ProductFacade> productFacadeMap, ExpressOrderModel snapShotModel) {
        //key=ProductNo
        Map<String, ProductInfoDto> productInfoDtoHashMap = new HashMap<>();

        // 主产品
        ProductInfoDto mainProduct = buildCCB2CMainProduct(wayBillQueryResponse, productFacadeMap);
        productInfoDtoHashMap.put(mainProduct.getProductNo(), mainProduct);

        // 增值产品
        List<ProductInfoDto> addedProductList = buildCCB2CAddedProduct(mainProduct, wayBillQueryResponse, expressOrderModelCreator, productFacadeMap);
        if (CollectionUtils.isNotEmpty(addedProductList)) {
            addedProductList.forEach(addedProduct -> {
                productInfoDtoHashMap.put(addedProduct.getProductNo(), addedProduct);
            });
        }

        expressOrderModelCreator.setProducts(new ArrayList<>(productInfoDtoHashMap.values()));
    }

    /**
     * 构建冷链B2C主产品
     */
    private ProductInfoDto buildCCB2CMainProduct(WayBillQueryResponse wayBillQueryResponse, Map<String, ProductFacade> productFacadeMap) {
        ProductInfoDto mainProduct = new ProductInfoDto();
        setProductAttrs(mainProduct, productFacadeMap, mainProduct.getProductNo());
        mainProduct.setProductType(ServiceProductTypeEnum.MAIN_PRODUCT.getCode());
        mainProduct.setProductName(ProductEnum.of(wayBillQueryResponse.getProductType()) == null ? null : ProductEnum.of(wayBillQueryResponse.getProductType()).getDesc());
        mainProduct.setProductNo(wayBillQueryResponse.getProductType());
        return mainProduct;
    }

    /**
     * 从查询结果的增值服务构建增值产品
     * <p>与快递类似，参考buildProductInfo方法</p>
     * {@link cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.WaybillInfoMappingUtil#buildProductInfo}
     */
    private List<ProductInfoDto> buildCCB2CAddedProduct(ProductInfoDto mainProduct, WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, Map<String, ProductFacade> productFacadeMap) {
        List<ProductInfoDto> addedProductList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(wayBillQueryResponse.getValueAddedServiceDTOList())) {
            for (ValueAddedServiceDTO dto : wayBillQueryResponse.getValueAddedServiceDTOList()) {
                if (null == dto) {
                    continue;
                }
                if(AddOnProductEnum.of(dto.getType()) == null){
                    continue;
                }
                ProductInfoDto productInfoDto = new ProductInfoDto();
                setProductAttrs(productInfoDto, productFacadeMap, dto.getType());
                productInfoDto.setProductNo(dto.getType());
                productInfoDto.setProductName(AddOnProductEnum.of(dto.getType()).getDesc());
                productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                productInfoDto.setParentNo(mainProduct.getProductNo());
                productInfoDto.setProductAttrs(dto.getOtherParams());
                addedProductList.add(productInfoDto);
            }
        }
        return addedProductList;
    }

    /**
     * 冷链B2C费用明细项
     * @param wayBillQueryResponse
     * @param mainProductInfoDto
     * @param productInfoDtoMap
     * @return
     */
    private List<FinanceDetailInfoDto> ccB2cBuildFinanceDetailInfo(WayBillQueryResponse wayBillQueryResponse,
                                                                 ProductInfoDto mainProductInfoDto,
                                                                 Map<String, ProductInfoDto> productInfoDtoMap){
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (WayBillDetailResponse detailResultDTO : wayBillQueryResponse.getWayBillDetailResponseList()){
            FinanceDetailInfoDto financeDetailInfoDto = new FinanceDetailInfoDto();
            // 产品编码，费用编码，费用名称，
            setCCB2CProductNo(detailResultDTO,mainProductInfoDto,financeDetailInfoDto,productInfoDtoMap);
            //设置运单费用信息到费用remark里面
            financeDetailInfoDto.setRemark(JSONUtils.beanToJSONDefault(detailResultDTO));
            if (financeDetailInfoDto.getRemark() != null && financeDetailInfoDto.getRemark().length() >= 500) {
                financeDetailInfoDto.setRemark(financeDetailInfoDto.getRemark().substring(0, 499));
            }
            // 设置折前金额
            MoneyInfoDto preAmount = new MoneyInfoDto();
            preAmount.setAmount(detailResultDTO.getAmount());
            preAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeDetailInfoDto.setPreAmount(preAmount);
            List<DiscountInfoDto> discountFacadeList = new ArrayList<>();
            BigDecimal afterDiscountAmount = preAmount.getAmount();//B2C折前和折后相等

            financeDetailInfoDto.setDiscountInfoDtos(discountFacadeList);
            // 更新折后金额
            MoneyInfoDto discountAmount = new MoneyInfoDto();
            discountAmount.setAmount(afterDiscountAmount);
            discountAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeDetailInfoDto.setDiscountAmount(discountAmount);
            //积分
            financeDetailInfoDto.setPointsInfoDto(toPointsInfo(detailResultDTO.getIntegralNumber(), detailResultDTO.getIntegralDeductAmount()));
            financeDetailInfoDtoList.add(financeDetailInfoDto);
        }
        return financeDetailInfoDtoList;
    }

    /**
     * @Description 冷链B2C费用转换编码
     **/
    private static void setCCB2CProductNo(WayBillDetailResponse detailResultDTO,ProductInfoDto mainProductInfoDto, FinanceDetailInfoDto financeDetailInfoDto,Map<String, ProductInfoDto> productInfoDtoMap) {
        LOGGER.info("CCB2C费用编码及产品编码赋值");
        //医药专送
        if ("YYZSYF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYZSYF");
            financeDetailInfoDto.setCostName("医药专送运费");
            financeDetailInfoDto.setProductNo(mainProductInfoDto.getProductNo());
        }
        if("YYKDBF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYKDBF");
            financeDetailInfoDto.setCostName("保价费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_MD_INSURED.getCode());
        }else if("YYKDQDFHF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYKDQDFHF");
            financeDetailInfoDto.setCostName("签单返还费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_MD_SIGN_RETURN_TOC.getCode());
        }else if("YYKDDSHKF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYKDDSHKF");
            financeDetailInfoDto.setCostName("代收货款费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_MD_COD_TOC.getCode());
        }else if("YYKDBZFWF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYKDBZFWF");
            financeDetailInfoDto.setCostName("包装服务费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_MD_PACKAGE_SERVICE_TOB.getCode());
        }else if("YYKDZDQSF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYKDZDQSF");
            financeDetailInfoDto.setCostName("指定签收费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_MD_DESIGNATED_SIGN.getCode());
        }else if("YYKDXSZTF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYKDXSZTF");
            financeDetailInfoDto.setCostName("协商再投费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_MD_NEGOTIATION_REDELIVERY.getCode());
        }else if("YYKDSHRCF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYKDSHRCF");
            financeDetailInfoDto.setCostName("送货入仓费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_MD_DELIVERY_INTO_WAREHOUSE_TOB.getCode());
        }
    }

    /**
     * 设置冷链B2B产品服务信息
     **/
    private void buildCCB2BProductInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, Map<String, ProductFacade> productFacadeMap, ExpressOrderModel snapShotModel) {
        //key=ProductNo
        Map<String, ProductInfoDto> productInfoDtoHashMap = new HashMap<>();

        // 主产品
        ProductInfoDto mainProduct = buildCCB2BMainProduct(wayBillQueryResponse, productFacadeMap);
        productInfoDtoHashMap.put(mainProduct.getProductNo(), mainProduct);

        // 增值产品
        List<ProductInfoDto> addedProductList = buildCCB2BAddedProduct(mainProduct, wayBillQueryResponse, expressOrderModelCreator, productFacadeMap);
        if (CollectionUtils.isNotEmpty(addedProductList)) {
            addedProductList.forEach(addedProduct -> {
                productInfoDtoHashMap.put(addedProduct.getProductNo(), addedProduct);
            });
        }

        expressOrderModelCreator.setProducts(new ArrayList<>(productInfoDtoHashMap.values()));
    }

    /**
     * 构建冷链B2B主产品
     */
    private ProductInfoDto buildCCB2BMainProduct(WayBillQueryResponse wayBillQueryResponse, Map<String, ProductFacade> productFacadeMap) {
        ProductInfoDto mainProduct = new ProductInfoDto();
        setProductAttrs(mainProduct, productFacadeMap, mainProduct.getProductNo());
        mainProduct.setProductType(ServiceProductTypeEnum.MAIN_PRODUCT.getCode());
        mainProduct.setProductName(ProductEnum.of(wayBillQueryResponse.getProductType()) == null ? null : ProductEnum.of(wayBillQueryResponse.getProductType()).getDesc());
        mainProduct.setProductNo(wayBillQueryResponse.getProductType());
        return mainProduct;
    }

    /**
     * 从查询结果的增值服务构建增值产品
     * <p>与快递类似，参考buildProductInfo方法</p>
     * {@link cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.WaybillInfoMappingUtil#buildProductInfo}
     */
    private List<ProductInfoDto> buildCCB2BAddedProduct(ProductInfoDto mainProduct, WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, Map<String, ProductFacade> productFacadeMap) {
        List<ProductInfoDto> addedProductList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(wayBillQueryResponse.getValueAddedServiceDTOList())) {
            for (ValueAddedServiceDTO dto : wayBillQueryResponse.getValueAddedServiceDTOList()) {
                if (null == dto) {
                    continue;
                }
                if(AddOnProductEnum.of(dto.getType()) == null){
                    continue;
                }
                if(AddOnProductEnum.PACKAGE_SERVICE_TOB.getCode().equals(dto.getType())) {
                    continue;
                }
                if(AddOnProductEnum.EXPRESS_CONSUMABLES.getCode().equals(dto.getType())) {
                    continue;
                }
                ProductInfoDto productInfoDto = new ProductInfoDto();
                setProductAttrs(productInfoDto, productFacadeMap, dto.getType());
                productInfoDto.setProductNo(dto.getType());
                productInfoDto.setProductName(AddOnProductEnum.of(dto.getType()).getDesc());
                productInfoDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                productInfoDto.setParentNo(mainProduct.getProductNo());
                productInfoDto.setProductAttrs(dto.getOtherParams());
                addedProductList.add(productInfoDto);
            }
        }
        return addedProductList;
    }

    /**
     * 冷链B2B费用明细项
     * @param wayBillQueryResponse
     * @param mainProductInfoDto
     * @param productInfoDtoMap
     * @return
     */
    private List<FinanceDetailInfoDto> ccB2bBuildFinanceDetailInfo(WayBillQueryResponse wayBillQueryResponse,
                                                                   ProductInfoDto mainProductInfoDto,
                                                                   Map<String, ProductInfoDto> productInfoDtoMap){
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (WayBillDetailResponse detailResultDTO : wayBillQueryResponse.getWayBillDetailResponseList()){
            FinanceDetailInfoDto financeDetailInfoDto = new FinanceDetailInfoDto();
            // 产品编码，费用编码，费用名称，
            setCCB2BProductNo(detailResultDTO,mainProductInfoDto,financeDetailInfoDto,productInfoDtoMap);
            //设置运单费用信息到费用remark里面
            financeDetailInfoDto.setRemark(JSONUtils.beanToJSONDefault(detailResultDTO));
            if (financeDetailInfoDto.getRemark() != null && financeDetailInfoDto.getRemark().length() >= 500) {
                financeDetailInfoDto.setRemark(financeDetailInfoDto.getRemark().substring(0, 499));
            }
            // 设置折前金额
            MoneyInfoDto preAmount = new MoneyInfoDto();
            preAmount.setAmount(detailResultDTO.getAmount());
            preAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeDetailInfoDto.setPreAmount(preAmount);
            List<DiscountInfoDto> discountFacadeList = new ArrayList<>();
            BigDecimal afterDiscountAmount = preAmount.getAmount();//B2C折前和折后相等

            financeDetailInfoDto.setDiscountInfoDtos(discountFacadeList);
            // 更新折后金额
            MoneyInfoDto discountAmount = new MoneyInfoDto();
            discountAmount.setAmount(afterDiscountAmount);
            discountAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            financeDetailInfoDto.setDiscountAmount(discountAmount);
            //积分
            financeDetailInfoDto.setPointsInfoDto(toPointsInfo(detailResultDTO.getIntegralNumber(), detailResultDTO.getIntegralDeductAmount()));
            financeDetailInfoDtoList.add(financeDetailInfoDto);
        }
        return financeDetailInfoDtoList;
    }

    /**
     * @Description 冷链B2B费用转换编码
     **/
    private static void setCCB2BProductNo(WayBillDetailResponse detailResultDTO,ProductInfoDto mainProductInfoDto, FinanceDetailInfoDto financeDetailInfoDto,Map<String, ProductInfoDto> productInfoDtoMap) {
        LOGGER.info("CCB2B费用编码及产品编码赋值");
        if ("B2BLLLD".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("B2BLLLD");
            financeDetailInfoDto.setCostName("B2B冷链卡班");
            financeDetailInfoDto.setProductNo(mainProductInfoDto.getProductNo());
        }  else if ("B2BLLCP".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("B2BLLCP");
            financeDetailInfoDto.setCostName("B2B冷链-城配");
            financeDetailInfoDto.setProductNo(mainProductInfoDto.getProductNo());
        } else if ("B2BLLZC".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("B2BLLZC");
            financeDetailInfoDto.setCostName("B2B冷链-整车");
            financeDetailInfoDto.setProductNo(mainProductInfoDto.getProductNo());
        } else if ("YYLDYS".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYLDYS");
            financeDetailInfoDto.setCostName("医药零担运输");
            financeDetailInfoDto.setProductNo(mainProductInfoDto.getProductNo());
        } else if ("YYLDDPYS".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYLDDPYS");
            financeDetailInfoDto.setCostName("医药零担大票运输");
            financeDetailInfoDto.setProductNo(mainProductInfoDto.getProductNo());
        } else if ("YLLDYF".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YLLDYF");
            financeDetailInfoDto.setCostName("医冷零担运费");
            financeDetailInfoDto.setProductNo(mainProductInfoDto.getProductNo());
        } else if ("YYZCYS".equals(detailResultDTO.getFeeType())){
            financeDetailInfoDto.setCostNo("YYZCYS");
            financeDetailInfoDto.setCostName("医药整车运输");
            financeDetailInfoDto.setProductNo(mainProductInfoDto.getProductNo());
        }

        if ("LLKBLS".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("LLKBLS");
            financeDetailInfoDto.setCostName("B2B冷链卡班揽收");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_TO_DOOR_PICKUP_TOB.getCode());
        }else if ("B2BLLCPLS".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("B2BLLCPLS");
            financeDetailInfoDto.setCostName("B2B冷链-城配揽收");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_TO_DOOR_PICKUP_TOB.getCode());
        }else if ("LLKBPS".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("LLKBPS");
            financeDetailInfoDto.setCostName("B2B冷链卡班派送");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_TO_DOOR_DELIVERY_TOB.getCode());
        }else if ("B2BLLZCF".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("B2BLLZCF");
            financeDetailInfoDto.setCostName("B2B冷链暂存费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_KD_STAGING_TOB.getCode());
        }else if ("LLB2BJYPF".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("LLB2BJYPF");
            financeDetailInfoDto.setCostName("冷链B2B检疫票费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_DK_QUARANTINE_TICKET_TOB.getCode());
        }else if ("B2BLLZHC".equals(detailResultDTO.getFeeType())) {
            if(productInfoDtoMap.containsKey(AddOnProductEnum.CC_LOADING_CAR_TOB.getCode())){
                financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_LOADING_CAR_TOB.getCode());
            }else if(productInfoDtoMap.containsKey(AddOnProductEnum.CC_MD_TO_DOOR_PICKUP_TOB.getCode())){
                financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_MD_TO_DOOR_PICKUP_TOB.getCode());
            }else{
                LOGGER.info("CCB2B费用编码及产品编码赋值,B2BLLZHC未识别到对应的增值产品");
                return;
            }
            financeDetailInfoDto.setCostNo("B2BLLZHC");
            financeDetailInfoDto.setCostName("B2B冷链装车");
        }else if ("B2BLLXC".equals(detailResultDTO.getFeeType())) {
            if(productInfoDtoMap.containsKey(AddOnProductEnum.CC_UNLOAD_CAR_TOB.getCode())){
                financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_UNLOAD_CAR_TOB.getCode());
            }else if(productInfoDtoMap.containsKey(AddOnProductEnum.CC_MD_TO_DOOR_DELIVERY_TOB.getCode())){
                financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_MD_TO_DOOR_DELIVERY_TOB.getCode());
            }else{
                LOGGER.info("CCB2B费用编码及产品编码赋值,B2BLLXC未识别到对应的增值产品");
                return;
            }
            financeDetailInfoDto.setCostNo("B2BLLXC");
            financeDetailInfoDto.setCostName("B2B冷链卸车");
        }else if ("B2BLLQDFH".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("B2BLLQDFH");
            financeDetailInfoDto.setCostName("B2B冷链签单返还");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_LL_SIGN_RETURN_TOB.getCode());
        }else if ("B2BLLSHRC".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("B2BLLSHRC");
            financeDetailInfoDto.setCostName("B2B冷链送货入仓");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_B2B_LL_DELIVERY_TO_WAREHOUSE.getCode());
        }else if ("YYBF".equals(detailResultDTO.getFeeType())) {
            if(productInfoDtoMap.containsKey(AddOnProductEnum.CC_B2B_MD_INSURED_VALUE.getCode())){
                financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_B2B_MD_INSURED_VALUE.getCode());
            }else if(productInfoDtoMap.containsKey(AddOnProductEnum.CC_SMALL_INSURED_TOB.getCode())){
                financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_SMALL_INSURED_TOB.getCode());
            }else{
                LOGGER.info("CCB2B费用编码及产品编码赋值,YYBF未识别到对应的增值产品");
                return;
            }
            financeDetailInfoDto.setCostNo("YYBF");
            financeDetailInfoDto.setCostName("医药保费");
        }else if ("YYDSHKSXF".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYDSHKSXF");
            financeDetailInfoDto.setCostName("医药代收货款手续费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_B2B_MD_COD.getCode());
        }else if ("YYXXB".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYXXB");
            financeDetailInfoDto.setCostName("医药箱箱保");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_BOX_BOX_INSURED_TOB.getCode());
        }else if ("YYZHSLFWF".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYZHSLFWF");
            financeDetailInfoDto.setCostName("医药重货上楼服务费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_B2B_MD_HEAVY_UPSTAIRS.getCode());
        }else if ("YYSHRC".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYSHRC");
            financeDetailInfoDto.setCostName("医药送货入仓");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_B2B_MD_DELIVERY_TO_WAREHOUSE.getCode());
        }else if ("YYZC".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYZC");
            financeDetailInfoDto.setCostName("医药装车");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_LOADING_UNLOAD_CAR_TOB.getCode());
        }else if ("YYXC".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYXC");
            financeDetailInfoDto.setCostName("医药卸车");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_LOADING_UNLOAD_CAR_TOB.getCode());
        }else if ("LLKBXSF".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("LLKBXSF");
            financeDetailInfoDto.setCostName("冷链卡班消杀费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_STERILIZATION_TOB.getCode());
        }else if ("YYBF".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYBF");
            financeDetailInfoDto.setCostName("医药保费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_SMALL_INSURED_TOB.getCode());
        }else if ("LLJZSC".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("LLJZSC");
            financeDetailInfoDto.setCostName("冷链精准送仓");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_DELIVERY_TO_WAREHOUSE_TOB.getCode());
        }else if ("B2BLLBF".equals(detailResultDTO.getFeeType())) {
            if(productInfoDtoMap.containsKey(AddOnProductEnum.CC_B2B_LL_INSURED_VALUE.getCode())){
                financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_B2B_LL_INSURED_VALUE.getCode());
            }else if(productInfoDtoMap.containsKey(AddOnProductEnum.LL_ZZ_BJ.getCode())){
                financeDetailInfoDto.setProductNo(AddOnProductEnum.LL_ZZ_BJ.getCode());
            }else{
                LOGGER.info("CCB2B费用编码及产品编码赋值,B2BLLBF未识别到对应的增值产品");
                return;
            }
            financeDetailInfoDto.setCostNo("B2BLLBF");
            financeDetailInfoDto.setCostName("B2B冷链保费");
        }else if ("LLZHSL".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("LLZHSL");
            financeDetailInfoDto.setCostName("冷链重货上楼");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_B2B_LL_HEAVY_UPSTAIRS.getCode());
        }else if ("YYQDFH".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYQDFH");
            financeDetailInfoDto.setCostName("医药签单返还");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_MD_SIGN_RETURN_TOB.getCode());
        }else if ("YYZYTJF".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYZYTJF");
            financeDetailInfoDto.setCostName("医药资源调节费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_SPRING_FESTIVAL_SURCHARGE_TOB.getCode());
        }else if ("YYBZF".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYBZF");
            financeDetailInfoDto.setCostName("医药包装费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_B2B_MD_PACKAGE_SERVICE.getCode());
        }else if ("YYSYF".equals(detailResultDTO.getFeeType())) {
            financeDetailInfoDto.setCostNo("YYSYF");
            financeDetailInfoDto.setCostName("医药送院费");
            financeDetailInfoDto.setProductNo(AddOnProductEnum.CC_IN_HOUSE_SERVICES_TOB.getCode());
        }
    }


    /**
     * 是否使用特惠送运营模式
     * 特惠送 ed-m-0001
     * 函速达 ed-m-0017
     * 特惠包裹 ed-m-0012
     **/
    private boolean isUseTHSOperationMode(String productNo) {
        if (StringUtils.isBlank(productNo)) {
            return false;
        }
        return ProductEnum.THS.getCode().equals(productNo)
                || ProductEnum.HSD.getCode().equals(productNo)
                || ProductEnum.THBG.getCode().equals(productNo);
    }


    /**
     * @Description 设置客户信息
     * @Param wayBillResponse
     * @Param facadeRequest
     * @Return void
     * @Throws
     **/
    private void buildCustomerInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator) {
        ExtendMessageDTO extendMessageDTO = wayBillQueryResponse.getExtendMessageDTO();
        if(null == extendMessageDTO || null == extendMessageDTO.getSubPin()){
            return;
        }
        CustomerInfoDto customerInfoDto = new CustomerInfoDto();
        if(null != extendMessageDTO.getSubPin()){
            customerInfoDto.setStorePin(extendMessageDTO.getSubPin());
        }
        //todo 门店pin
        expressOrderModelCreator.setCustomerInfo(customerInfoDto);
    }

    /**
     * 履约信息构建
     * @param wayBillQueryResponse
     * @param expressOrderModelCreator
     * @param snapShotCargoDelegate
     */
    public void buildFulfillmentInfo(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModelCreator expressOrderModelCreator, CargoDelegate snapShotCargoDelegate) {
        FulfillmentInfoDto fulfillmentInfoDto =new FulfillmentInfoDto();

        // 货品信息在托寄物维度上仅有一个对象
        Cargo cargo = (Cargo) snapShotCargoDelegate.firstCargo();

        //包裹数量
        if (wayBillQueryResponse.getPackageCount() != null) {
            QuantityInfoDto quantity = new QuantityInfoDto();
            quantity.setValue(new BigDecimal(wayBillQueryResponse.getPackageCount()));
            quantity.setUnit(cargo.getCargoQuantity().getUnit());
            fulfillmentInfoDto.setActualReceivedQuantity(quantity);
        }

        expressOrderModelCreator.setFulfillmentInfo(fulfillmentInfoDto);
    }
}