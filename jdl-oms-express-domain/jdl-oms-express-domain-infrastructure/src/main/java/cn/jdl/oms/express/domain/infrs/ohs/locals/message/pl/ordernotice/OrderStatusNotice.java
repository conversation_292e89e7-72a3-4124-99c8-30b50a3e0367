package cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ordernotice;

import cn.jdl.oms.core.model.RequestProfile;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 订单状态变更通知消息
 * 对应 EXPRESS_ORDER_STATUS 消息
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class OrderStatusNotice extends CommonDto {
    
    private RequestProfile profile;
    
    private Data data;
    
    /**
     * 消息属性
     */
    @JSONField(serialize = false)
    private Map<String, String> msgAttributes;
    
    @lombok.Data
    public static class Data implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 业务身份
         */
        private OrderBusinessIdentity businessIdentity;
        
        /**
         * 订单号
         */
        private String orderNo;
        
        /**
         * 业务单号
         */
        private String customOrderNo;
        
        /**
         * 订单类型
         */
        private Integer orderType;
        
        /**
         * 原状态
         */
        private Integer oldStatus;
        
        /**
         * 新状态
         */
        private Integer newStatus;
        
        /**
         * 状态变更时间
         */
        private Date statusChangeTime;
        
        /**
         * 变更原因
         */
        private String changeReason;
        
        /**
         * 操作人
         */
        private String operator;
        
        /**
         * 操作人类型
         */
        private Integer operatorType;
        
        /**
         * 扩展属性
         */
        private Map<String, String> extendProps;
    }
}
