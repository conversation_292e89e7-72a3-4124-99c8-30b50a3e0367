package cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ordernotice;

import cn.jdl.oms.core.model.RequestProfile;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 订单数据流水消息
 * 对应 EXPRESS_ORDER_DATA 消息
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class OrderDataFlowNotice extends CommonDto {
    
    private RequestProfile profile;
    
    private Data data;
    
    /**
     * 消息属性
     */
    @JSONField(serialize = false)
    private Map<String, String> msgAttributes;
    
    @lombok.Data
    public static class Data implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 业务身份
         */
        private OrderBusinessIdentity businessIdentity;
        
        /**
         * 订单号
         */
        private String orderNo;
        
        /**
         * 业务单号
         */
        private String customOrderNo;
        
        /**
         * 订单类型
         */
        private Integer orderType;
        
        /**
         * 操作类型
         */
        private String operationType;
        
        /**
         * 操作描述
         */
        private String operationDesc;
        
        /**
         * 操作时间
         */
        private Date operationTime;
        
        /**
         * 操作人
         */
        private String operator;
        
        /**
         * 操作人类型
         */
        private Integer operatorType;
        
        /**
         * 数据变更前内容
         */
        private String beforeData;
        
        /**
         * 数据变更后内容
         */
        private String afterData;
        
        /**
         * 变更字段
         */
        private String changedFields;
        
        /**
         * 流水类型
         */
        private String flowType;
        
        /**
         * 扩展属性
         */
        private Map<String, String> extendProps;
    }
}
