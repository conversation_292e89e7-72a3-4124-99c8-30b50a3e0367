package cn.jdl.oms.express.domain.infrs.acl.util;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.shared.common.constant.ModifySceneRuleConstants;
import org.apache.commons.collections4.MapUtils;

/**
 * 修改策略判断工具
 */
public class ModifySceneRuleUtil {

    /**
     * 获取修改策略，可能为null
     */
    public static String getModifySceneRule(ExpressOrderContext orderContext) {
        if (orderContext == null || orderContext.getOrderModel() == null) {
            return null;
        }
        return getModifySceneRule(orderContext.getOrderModel());
    }

    /**
     * 获取修改策略，可能为null
     */
    public static String getModifySceneRule(ExpressOrderModel orderModel) {
        if (orderModel == null || MapUtils.isEmpty(orderModel.getExtendProps())) {
            return null;
        }
        return orderModel.getExtendProps().get(ModifySceneRuleConstants.MODIFY_SCENE_RULE);
    }

    /**
     * 修改策略是否揽收前修改
     */
    public static boolean isBeforePickUp(ExpressOrderContext orderContext) {
        return isBeforePickUp(getModifySceneRule(orderContext));
    }

    /**
     * 修改策略是否揽收前修改
     */
    public static boolean isBeforePickUp(ExpressOrderModel orderModel) {
        return isBeforePickUp(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否揽收前修改
     */
    public static boolean isBeforePickUp(String modifySceneRule) {
        return ModifySceneRuleConstants.BEFORE_PICKUP.equals(modifySceneRule);
    }

    /**
     * 修改策略是否揽收后修改
     */
    public static boolean isAfterPickUp(ExpressOrderContext orderContext) {
        return isAfterPickUp(getModifySceneRule(orderContext));
    }

    /**
     * 修改策略是否揽收后修改
     */
    public static boolean isAfterPickUp(ExpressOrderModel orderModel) {
        return isAfterPickUp(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否揽收后修改
     */
    public static boolean isAfterPickUp(String modifySceneRule) {
        return ModifySceneRuleConstants.AFTER_PICKUP.equals(modifySceneRule);
    }

    /**
     * 修改策略是否揽收后改址
     */
    public static boolean isAfterPickUpReaddress(ExpressOrderContext orderContext) {
        return isAfterPickUpReaddress(getModifySceneRule(orderContext));
    }

    /**
     * 修改策略是否揽收后改址
     */
    public static boolean isAfterPickUpReaddress(ExpressOrderModel orderModel) {
        return isAfterPickUpReaddress(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否揽收后改址
     */
    public static boolean isAfterPickUpReaddress(String modifySceneRule) {
        return ModifySceneRuleConstants.AFTER_PICKUP_READDRESS.equals(modifySceneRule);
    }

    /**
     * 修改策略是否货品数量修改
     */
    public static boolean isModifyQuantity(ExpressOrderContext orderContext) {
        return isModifyQuantity(getModifySceneRule(orderContext));
    }

    /**
     * 修改策略是否货品数量修改
     */
    public static boolean isModifyQuantity(ExpressOrderModel orderModel) {
        return isModifyQuantity(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否货品数量修改
     */
    public static boolean isModifyQuantity(String modifySceneRule) {
        return ModifySceneRuleConstants.MODIFY_QUANTITY.equals(modifySceneRule);
    }

    /**
     * 修改策略是否仅修改收件人联系方式（姓名、电话、手机）
     */
    public static boolean isOnlyModifyConsigneeContactInformation(ExpressOrderContext orderContext) {
        return isOnlyModifyConsigneeContactInformation(getModifySceneRule(orderContext));
    }

    /**
     * 修改策略是否仅修改收件人联系方式（姓名、电话、手机）
     */
    public static boolean isOnlyModifyConsigneeContactInformation(ExpressOrderModel orderModel) {
        return isOnlyModifyConsigneeContactInformation(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否仅修改收件人联系方式（姓名、电话、手机）
     */
    public static boolean isOnlyModifyConsigneeContactInformation(String modifySceneRule) {
        return ModifySceneRuleConstants.ONLY_MODIFY_CONSIGNEE_CONTACT_INFORMATION.equals(modifySceneRule);
    }

    /**
     * 修改策略是否仅修改报关信息
     */
    public static boolean isOnlyModifyCustoms(ExpressOrderContext orderContext) {
        return isOnlyModifyCustoms(getModifySceneRule(orderContext));
    }

    /**
     * 修改策略是否仅修改报关信息
     */
    public static boolean isOnlyModifyCustoms(ExpressOrderModel orderModel) {
        return isOnlyModifyCustoms(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否仅修改报关信息
     */
    private static boolean isOnlyModifyCustoms(String modifySceneRule) {
        return ModifySceneRuleConstants.ONLY_MODIFY_CUSTOMS.equals(modifySceneRule);
    }

    /**
     * 修改策略是否销售修改
     */
    public static boolean isModifyBySalesperson(ExpressOrderModel orderModel) {
        return isModifyBySalesperson(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否销售修改
     */
    public static boolean isModifyBySalesperson(String modifySceneRule) {
        return ModifySceneRuleConstants.MODIFY_BY_SALESPERSON.equals(modifySceneRule);
    }

    /**
     * 修改策略是否新增压车费
     */
    public static boolean isInsertExtrudeFee(ExpressOrderModel orderModel) {
        return isInsertExtrudeFee(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否新增压车费
     */
    public static boolean isInsertExtrudeFee(String modifySceneRule) {
        return ModifySceneRuleConstants.INSERT_EXTRUDE_FEE.equals(modifySceneRule);
    }

    /**
     * 修改策略是否营业厅修改
     */
    public static boolean isModifyByBusinessHall(ExpressOrderModel orderModel) {
        return isModifyByBusinessHall(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否营业厅修改
     */
    public static boolean isModifyByBusinessHall(String modifySceneRule) {
        return ModifySceneRuleConstants.MODIFY_BY_BUSINESS_HALL.equals(modifySceneRule);
    }

    /**
     * 修改策略是否修改联系方式
     */
    public static boolean isModifyContactInformation(ExpressOrderModel orderModel) {
        return isModifyContactInformation(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否修改联系方式
     */
    public static boolean isModifyContactInformation(String modifySceneRule) {
        return ModifySceneRuleConstants.MODIFY_CONTACT_INFORMATION.equals(modifySceneRule);
    }

    /**
     * 修改策略是否仓出库发货
     */
    public static boolean isOutboundDelivery(ExpressOrderModel orderModel) {
        return isOutboundDelivery(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否仓出库发货
     */
    public static boolean isOutboundDelivery(String modifySceneRule) {
        return ModifySceneRuleConstants.OUTBOUND_DELIVERY.equals(modifySceneRule);
    }

    /**
     * 修改策略是否是特殊修改策略
     */
    public static boolean isSpecialModify(String modifySceneRule) {
        return ModifySceneRuleConstants.SPECIAL_MODIFY.equals(modifySceneRule);
    }

    /**
     * 修改策略是否 仅修改多地址审核状态
     */
    public static boolean isOnlyModifyMultiAddressVerifyStatus(String modifySceneRule) {
        return ModifySceneRuleConstants.ONLY_MODIFY_MULTI_ADDRESS_VERIFY_STATUS.equals(modifySceneRule);
    }

    /**
     * 修改策略是否是碳排放计算
     */
    public static boolean isCarbonEmissionCalculation(String modifySceneRule) {
        return ModifySceneRuleConstants.CARBON_EMISSION_CALCULATION.equals(modifySceneRule);
    }

    /**
     * 是否属于 内部修改
     *
     * @param orderModel
     * @return
     */
    public static boolean isPartOfInternalModify(ExpressOrderModel orderModel) {

        return isPartOfInternalModify(getModifySceneRule(orderModel));

    }

    /**
     * 是否属于 内部修改
     *
     * @param modifySceneRule
     * @return
     */
    public static boolean isPartOfInternalModify(String modifySceneRule) {

        // 目前内部修改 只有 修改/清除收发货人信息 场景，后续如有其他诉求，可在此添加。命名规范：internalModify开头
        return ModifySceneRuleConstants.INTERNAL_MODIFY_CONSIGNOR_AND_CONSIGNEE_INFO.equals(modifySceneRule)
                || ModifySceneRuleConstants.INTERNAL_MODIFY_RETURN_ORDER_INFO.equals(modifySceneRule)
                || ModifySceneRuleConstants.INTERNAL_MODIFY.equals(modifySceneRule);

    }

    /**
     * 修改策略是否特殊仓出库发货
     * 订单内部处理逻辑与outboundDelivery相同，区别是配运OFC识别此修改策略会更新运单包裹数
     */
    public static boolean isOutboundDeliveryCustom(ExpressOrderModel orderModel) {
        return isOutboundDeliveryCustom(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否特殊仓出库发货
     * 订单内部处理逻辑与outboundDelivery相同，区别是配运OFC识别此修改策略会更新运单包裹数
     */
    public static boolean isOutboundDeliveryCustom(String modifySceneRule) {
        return ModifySceneRuleConstants.OUTBOUND_DELIVERY_CUSTOM.equals(modifySceneRule);
    }

    /**
     * 修改策略是否仓出库发货/特殊仓出库发货
     */
    public static boolean isTypeOfOutboundDelivery(ExpressOrderModel orderModel) {
        return isOutboundDelivery(getModifySceneRule(orderModel)) || isOutboundDeliveryCustom(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否允许"派送中"修改期望送达时间
     *
     * @param orderModel
     * @return
     */
    public static boolean isModifyExpectDeliveryTimeOnDelivering(ExpressOrderModel orderModel) {
        return isModifyExpectDeliveryTimeOnDelivering(getModifySceneRule(orderModel));
    }

    public static boolean isModifyExpectDeliveryTimeOnDelivering(String modifySceneRule) {
        return ModifySceneRuleConstants.MODIFY_EXPECT_DELIVERY_TIME_ON_DELIVERING.equals(modifySceneRule);
    }

    /**
     * 修改策略是否运营模式
     */
    public static boolean isModifyOpMode(ExpressOrderModel orderModel) {
        return isModifyOpMode(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否运营模式
     */
    public static boolean isModifyOpMode(String modifySceneRule) {
        return ModifySceneRuleConstants.MODIFY_OP_MODE.equals(modifySceneRule);
    }

    //是否是大件内部修改收件人地址
    public static boolean isInternalModifyLASConsigneeInfo(String modifySceneRule) {
        // 内部修改收件人信息，只落库不下发
        return ModifySceneRuleConstants.INTERNAL_MODIFY_LAS_CONSIGNEE_INFO.equals(modifySceneRule);

    }

    /**
     * 修改策略是否仅修改补签标识
     */
    public static boolean isModifyReSignFlag(ExpressOrderModel orderModel) {
        return isModifyReSignFlag(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否仅修改补签标识
     */
    public static boolean isModifyReSignFlag(String modifySceneRule) {
        return ModifySceneRuleConstants.MODIFY_RE_SIGN_FLAG.equals(modifySceneRule);
    }

    /**
     * 修改策略是否仅修改订单标识
     */
    public static boolean isOnlyModifyOrder(String modifySceneRule) {
        return ModifySceneRuleConstants.ONLY_MODIFY_ORDER.equals(modifySceneRule);
    }

    /**
     * 修改策略是否内部修改逆向单
     */
    public static boolean isInternalModifyReturnOrderInfo(ExpressOrderModel orderModel) {
        return isInternalModifyReturnOrderInfo(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否内部修改逆向单
     */
    public static boolean isInternalModifyReturnOrderInfo(String modifySceneRule) {
        return ModifySceneRuleConstants.INTERNAL_MODIFY_RETURN_ORDER_INFO.equals(modifySceneRule);
    }

    public static boolean isModifyGuobuStatus(ExpressOrderModel orderModel) {
        return isModifyGuobuStatus(getModifySceneRule(orderModel));
    }

    public static boolean isModifyGuobuStatus(String modifySceneRule) {
        return ModifySceneRuleConstants.MODIFY_GUO_BU_STATUS.equals(modifySceneRule);
    }
    /**
     * 修改策略是否是修改国补采集信息
     */
    public static boolean isModifyGovSubsidyCollectionInformation(ExpressOrderModel orderModel) {
        return isModifyGovSubsidyCollectionInformation(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否是修改国补采集信息
     */
    public static boolean isModifyGovSubsidyCollectionInformation(String modifySceneRule) {
        return ModifySceneRuleConstants.MODIFY_GOV_SUBSIDY_COLLECTION_INFORMATION.equals(modifySceneRule);
    }


    /**
     * 是否无任务揽收策略
     *
     * @param orderModel
     * @return
     */
    public static boolean isNoTaskFinishCollect(ExpressOrderModel orderModel) {
        String modifyScene = getModifySceneRule(orderModel);
        return ModifySceneRuleConstants.NO_TASK_FINISH_COLLECT.equals(modifyScene);
    }

    /**
     * 修改策略是否修改应收和实收
     */
    public static boolean isModifyReceivableAndRealPay(ExpressOrderModel orderModel) {
        return isModifyReceivableAndRealPay(getModifySceneRule(orderModel));
    }

    /**
     * 修改策略是否修改应收和实收
     */
    public static boolean isModifyReceivableAndRealPay(String modifySceneRule) {
        return ModifySceneRuleConstants.MODIFY_RECEIVABLE_AND_REAL_PAY.equals(modifySceneRule);
    }

}
