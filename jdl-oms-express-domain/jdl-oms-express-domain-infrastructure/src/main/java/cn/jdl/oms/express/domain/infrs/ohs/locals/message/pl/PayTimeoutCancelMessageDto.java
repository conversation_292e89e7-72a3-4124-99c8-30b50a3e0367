package cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import lombok.Data;

import java.util.Date;

@Data
public class PayTimeoutCancelMessageDto extends CommonDto {


    /**
     * API 请求参数
     */
    private RequestProfile payCancelRequestProfile;

    /**
     * 纯配订单领域模型业务身份体系
     */
    private OrderBusinessIdentity orderBusinessIdentity;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 是否修改收货人信息
     */
    private Boolean modifyConsignee = false;

    /**
     * 通过修改记录号支付的修改记录号
     */
    private String payRecordNo;

    /**
     * 支付截止时间
     */
    private Date payDeadline;

}
