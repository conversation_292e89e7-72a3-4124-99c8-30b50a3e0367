package cn.jdl.oms.express.domain.service.impl;

import cn.jdl.batrix.core.flow.domain.BOutputMessage;
import cn.jdl.batrix.core.flow.engine.BFlowContext;
import cn.jdl.batrix.core.flow.engine.BFlowInitConfig;
import cn.jdl.batrix.core.flow.engine.adapter.BFlowEngine;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.ModifyExpressOrderResult;
import cn.jdl.oms.express.domain.flow.antirelock.AntiRelockFlowNode;
import cn.jdl.oms.express.domain.flow.bind.DeliveryPickupSyncBindFlowNode;
import cn.jdl.oms.express.domain.flow.customer.AsyncPrechargeFlowNode;
import cn.jdl.oms.express.domain.flow.enquiry.AsynEnquiryFlowNode;
import cn.jdl.oms.express.domain.flow.enquiry.AsyncStandardProductAndDiscountEnquiryFlowNode;
import cn.jdl.oms.express.domain.flow.init.InitFlowNode;
import cn.jdl.oms.express.domain.flow.integral.IntegralReleaseFlowNode;
import cn.jdl.oms.express.domain.flow.iot.IotReleaseFlowNode;
import cn.jdl.oms.express.domain.flow.ious.IousReleaseFlowNode;
import cn.jdl.oms.express.domain.flow.order.OrderFieldDataNotifyFlowNode;
import cn.jdl.oms.express.domain.flow.orderquantity.OrderQuantityReplaceFlowNode;
import cn.jdl.oms.express.domain.flow.ordertrack.OrderTrackFlowNode;
import cn.jdl.oms.express.domain.flow.pay.RefundFlowNode;
import cn.jdl.oms.express.domain.flow.repository.GetOrderRepositoryFlowNode;
import cn.jdl.oms.express.domain.flow.repository.OrderPreprocessUpdateFlowNode;
import cn.jdl.oms.express.domain.flow.repository.OrderRepositoryFlowNode;
import cn.jdl.oms.express.domain.flow.serviceEnquiry.AsyncCancelServiceEnquiryOrderFLowNode;
import cn.jdl.oms.express.domain.flow.trace.TraceUploadFlowNode;
import cn.jdl.oms.express.domain.flow.wechatpayment.WeChatPaymentCancelFlowNode;
import cn.jdl.oms.express.domain.infrs.acl.util.ReaddressSettlementTypeUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.DpDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.facade.PayTimeoutCancelJmqFacade;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.service.IModifyExpressOrderDomainService;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.utils.ContextInfoUtil;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.horz.ext.repository.GetOrderRepositoryExtension;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.FlowEngineConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyResultEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.exception.DomainServiceException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName ModifyExpressOrderDomainService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/27 6:36 下午
 * @ModifyDate 2021/3/27 6:36 下午
 * @Version 1.0
 */
@Service
public class ModifyExpressOrderDomainService implements IModifyExpressOrderDomainService {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyExpressOrderDomainService.class);

    @Resource
    private UmpUtil umpUtil;

    /**
     * 初始化节点
     */
    @Resource
    private InitFlowNode initFlowNode;
    /**
     * 持久化节点
     */
    @Resource
    private OrderRepositoryFlowNode orderRepositoryFlowNode;
    /**
     * 修改防重/并发
     */
    @Resource
    private AntiRelockFlowNode antiRelockFlowNode;

    /**
     * 持久化节点
     */
    @Resource
    private OrderRepositoryFlowNode repositoryFlowNode;

    /**
     * 鸡毛信释放
     */
    @Resource
    private IotReleaseFlowNode iotReleaseFlowNode;
    /**
     * 白条释放
     */
    @Resource
    private IousReleaseFlowNode iousReleaseFlowNode;


    @Resource
    private GetOrderRepositoryFlowNode getOrderRepositoryFlowNode;

    @Resource
    private GetOrderRepositoryExtension getOrderRepositoryExtension;

    @Resource
    private WeChatPaymentCancelFlowNode weChatPaymentCancelFlowNode;

    @Resource
    private IntegralReleaseFlowNode integralReleaseFlowNode;

    /** 订单信息流水节点 */
    @Resource
    private OrderFieldDataNotifyFlowNode orderFieldDataNotifyFlowNode;

    @Resource
    private OrderQuantityReplaceFlowNode orderQuantityReplaceFlowNode;

    @Resource
    private OrderPreprocessUpdateFlowNode orderPreprocessUpdateFlowNode;

    @Resource
    private OrderTrackFlowNode orderTrackFlowNode;

    @Resource
    private TraceUploadFlowNode traceUploadFlowNode;

    /**
     * 异步询价写台账
     */
    @Resource
    private AsynEnquiryFlowNode asynEnquiryFlowNode;

    /**
     * 异步退款flowNode
     */
    @Resource
    private RefundFlowNode refundFlowNode;

    @Resource
    private PayTimeoutCancelJmqFacade payTimeoutCancelJmqFacade;

    @Resource
    private AsyncCancelServiceEnquiryOrderFLowNode asyncCancelServiceEnquiryOrderFLowNode;

    @Resource
    private AsyncPrechargeFlowNode asyncPrechargeFlowNode;

    @Resource
    private AsyncStandardProductAndDiscountEnquiryFlowNode asyncStandardProductAndDiscountEnquiryFlowNode;

    @Resource
    private DeliveryPickupSyncBindFlowNode deliveryPickupSyncBindFlowNode;

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 纯配修改领域服务修改领域服务
     * <AUTHOR>
     * @createDate 2021/3/27 6:36 下午
     * @lastModify update by wangjingzhao 2021-06-05 21:29:40 增加batrix流程图外无自动回滚策略显示调用回滚策略
     */
    @Override
    public ModifyExpressOrderResult modifyOrder(ExpressOrderContext context) throws DomainServiceException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".modifyOrder"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        ModifyExpressOrderResult modifyExpressOrderResult = new ModifyExpressOrderResult();
        //初始化引擎
        BFlowEngine iDomainFlowEngine = new BFlowEngine.Builder()
                .code(FlowEngineConstants.JDL_CP_EXPRESS_ORDER_MODIFY_ENGINE_CODE)
                .name(FlowEngineConstants.JDL_CP_EXPRESS_ORDER_MODIFY_ENGINE_NAME)
                .domainCode(DomainConstants.EXPRESS_ORDER_DOMIAN_CODE).build();
        ExpressOrderModel orderModel = context.getOrderModel();
        try {
            LOGGER.info("纯配修改领域服务修改领域对象入参:context={}", JSONUtils.beanToJSONDefault(context));
            //流程解析数据
            BFlowInitConfig bFlowInitConfig = new BFlowInitConfig();
            bFlowInitConfig.setCode(FlowEngineConstants.JDL_CP_EXPRESS_ORDER_MODIFY_ENGINE_CODE);
            bFlowInitConfig.setName(FlowEngineConstants.JDL_CP_EXPRESS_ORDER_MODIFY_ENGINE_NAME);
            //流程编排
            BFlowContext flowContext = new BFlowContext(context);
            BOutputMessage bOutputMessage = null;
            //流程初始化
            InputMessage inputMessage = new InputMessage(context);
            //获取订单详情
            getOrderRepositoryFlowNode.call(inputMessage);
            // 融合业务身份特殊逻辑，放在所有节点之前。
            if (UnitedBusinessIdentityUtil.isUnitedIdentity(context)) {
                // 非接单场景，需提前获取订单详情，并更新上下文
                UnitedBusinessIdentityUtil.convertToReal(context, true);
            }
            //并发锁
            iDomainFlowEngine.pushCallBack(antiRelockFlowNode.call(inputMessage).getCallBackInterface());
            // 初始化
            iDomainFlowEngine.pushCallBack(initFlowNode.call(inputMessage).getCallBackInterface());
            //根据原单补全订单信息
            //completOrderInfoByOrg(context.getOrderModel());
            //batrix 流程编排
            bOutputMessage = iDomainFlowEngine.start(bFlowInitConfig, flowContext);
            if (null != bOutputMessage && bOutputMessage.isBlock() && bOutputMessage.isException()) {
                LOGGER.info("纯配修改领域修改服务流程引擎执行异常,需中断", bOutputMessage.getBException());
                throw bOutputMessage.getBException();
            }

            //订单持久化节点
            iDomainFlowEngine.pushCallBack(repositoryFlowNode.call(inputMessage).getCallBackInterface());

            modifyExpressOrderResult.setCode(ModifyResultEnum.MODIFY_SUCCESS.getCode());
            //预分拣结果
            modifyExpressOrderResult.setPresortResult(context.getPresortResult());
            LOGGER.info("纯配修改领域修改服务处理结束,出参, dooReceiveResult={}", JSONUtils.beanToJSONDefault(modifyExpressOrderResult));

            if (ContextInfoUtil.isInternalModify(context)) {
                LOGGER.info("内部修改，跳过后续流程");
                return modifyExpressOrderResult;
            }

            try {
                iDomainFlowEngine.pushCallBack(asyncCancelServiceEnquiryOrderFLowNode.call(inputMessage).getCallBackInterface());
            } catch (Exception e) {
                LOGGER.error("修改节点执行异常,服务单异步取消失败", e);
            }

            try {
                iDomainFlowEngine.pushCallBack(orderQuantityReplaceFlowNode.call(inputMessage).getCallBackInterface());
            } catch (Exception e) {
                LOGGER.error("修改节点执行异常,单量限制-订单数量替换失败", e);
            }

            try {
                iDomainFlowEngine.pushCallBack(orderPreprocessUpdateFlowNode.call(inputMessage).getCallBackInterface());
            } catch (Exception e) {
                LOGGER.error("修改节点执行异常, 订单预处理信息更新执行失败", e);
            }

            try {
                iDomainFlowEngine.pushCallBack(iotReleaseFlowNode.call(inputMessage).getCallBackInterface());//鸡毛信释放
            } catch (Exception e) {
                LOGGER.error("纯配领域能力-鸡毛信设备释放资源能力执行异常: ", e);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_IOT_RELEASE_EXCEPTION_ALARM, "修改鸡毛信资源释放异常,order=" + orderModel.orderNo());
                context.putUnblockedExceptionValue(iotReleaseFlowNode.getClass().getName(), e.getMessage());
            }
            try {
                iDomainFlowEngine.pushCallBack(iousReleaseFlowNode.call(inputMessage).getCallBackInterface());//白条释放
            } catch (Exception e) {
                LOGGER.error("纯配接单领域能力-白条预授取消活动执行异常: ", e);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_IOUS_RELEASE_EXCEPTION_ALARM, "修改白条预授权释放异常，orderNo=" + orderModel.orderNo());
                context.putUnblockedExceptionValue(iousReleaseFlowNode.getClass().getName(), e.getMessage());
            }

            try {
                iDomainFlowEngine.pushCallBack(weChatPaymentCancelFlowNode.call(inputMessage).getCallBackInterface());//微信代扣
            } catch (Exception e) {
                LOGGER.error("纯配领域能力修改-订单微信免密单修改取消活动执行异常: ", e);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_WECHAT_PAYMENT_EXCEPTION_ALARM, "修改订单微信免密单释放异常,orderNo=" + orderModel.orderNo());
                context.putUnblockedExceptionValue(weChatPaymentCancelFlowNode.getClass().getName(), e.getMessage());
            }
            try {
                iDomainFlowEngine.pushCallBack(integralReleaseFlowNode.call(inputMessage).getCallBackInterface());//积分释放
            } catch (Exception e) {
                LOGGER.error("纯配修改领域能力-积分释放能力活动执行异常:", e);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_INTEGRAL_RELEASE_EXCEPTION_ALARM, "修改积分释放pdq异常，orderNo=" + orderModel.orderNo());
                context.putUnblockedExceptionValue(integralReleaseFlowNode.getClass().getName(), e.getMessage());
            }

            //fixme 改址一单到底异步处理流程
            if (orderModel.isReaddress1Order2End() || orderModel.isKKInterceptionThroughOrderRecord()) {
                LOGGER.info("一单到底后置异步处理,orderNo:{}", orderModel.orderNo());
                //todo 后续考虑封装成单独的通用能力
                // 需要支付的
                if (OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.READDRESS_NEED_PAY.getCode()))
                        && !orderModel.isKKInterceptionThroughOrderRecord()){//拦截一单到底-不取消 未支付的终端妥投卡控 需要同步下发
                    try {
                        LOGGER.info("改址一单到底-先款支付单，设置超时取消任务,orderNo:{}", orderModel.orderNo());
                        payTimeoutCancelJmqFacade.sendAsyncPayTimeoutCancelMQ(context);
                    } catch (Exception e) {
                        LOGGER.error("改址一单到底-先款支付单，设置超时取消任务异常", e);
                        Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_READDRESS_PAY_TIMEOUT_CREATE_FAIL, "改址一单到底-先款支付单，设置超时取消任务异常,traceId=" + context.getRequestProfile().getTraceId());
                        context.putUnblockedExceptionValue(payTimeoutCancelJmqFacade.getClass().getName(), e.getMessage());
                    }
                }
                //后款-异步询价
                // 原单到付-拦截一单到底-改址月结：当【特殊来源 = 淘天】时，改址前置校验、修改服务放开原单到付、改址月结卡控；（改址结算方式虽然为月结，但按到付现结-后款处理）
                if (null != context.getModifyRecordDelegate()
                        && null != context.getModifyRecordDelegate().getLastEnabledThroughOrderModifyRecord()) {
                    ModifyRecord lastEnabledThroughOrderModifyRecord = context.getModifyRecordDelegate().getLastEnabledThroughOrderModifyRecord();
                    ReaddressRecordDetailInfo readdressRecordDetail = (ReaddressRecordDetailInfo) lastEnabledThroughOrderModifyRecord.getModifyRecordDetail();
                    Integer paymentStage = readdressRecordDetail.getFinance().getPaymentStage();
                    if (ReaddressSettlementTypeUtil.isTaoTianOriginalDaoFu(context.getOrderModel())
                            || (PaymentStageEnum.CASHONDELIVERY == PaymentStageEnum.of(paymentStage)
                            && !OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.SKIP_ENQUIRY_ORDER_BANK.getCode()))//无需跳过询价写账
                            && !BusinessUnitEnum.CN_JDL_C2B.getCode().equals(orderModel.getBusinessIdentity().getBusinessUnit())//取件单目前只有月结到月结，直接跳过；后续随业务变化调整
                            && !DpDeliveryOrderSignUtil.flag(context.getOrderModel()))){//德邦落地配目前只有月结到月结，直接跳过；后续随业务变化调整
                        try {
                            LOGGER.info("一单到底-后款异步询价,orderNo:{}", orderModel.orderNo());
                            iDomainFlowEngine.pushCallBack(asynEnquiryFlowNode.call(inputMessage).getCallBackInterface());
                        } catch (Exception e) {
                            LOGGER.error("改址一单到底-异步询价异常", e);
                            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "改址一单到底-异步询价异常,traceId=" + context.getRequestProfile().getTraceId());
                            context.putUnblockedExceptionValue(asynEnquiryFlowNode.getClass().getName(), e.getMessage());
                        }

                    }
                }

                //先款-改址最新记录差额<0
                if (OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.READDRESS_NEED_REFUND.getCode()))) {
                    try {
                        iDomainFlowEngine.pushCallBack(refundFlowNode.call(inputMessage).getCallBackInterface());
                    } catch (Exception e) {
                        LOGGER.error("改址一单到底-异步退款异常", e);
                        Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REFUND_EXCEPTION_ALARM, "改址一单到底-异步退款异常,traceId=" + context.getRequestProfile().getTraceId());
                        context.putUnblockedExceptionValue(refundFlowNode.getClass().getName(), e.getMessage());
                    }
                }
            }

            // 送取同步绑定或解绑关联单流程节点
            if (null != context.getExtInfo(ContextInfoEnum.DELIVERY_PICKUP_SYNC_BIND_OPERATE_TYPE.getCode())) {
                LOGGER.info("涉及送取同步，执行送取同步绑定或解绑关联单流程节点");
                iDomainFlowEngine.pushCallBack(deliveryPickupSyncBindFlowNode.call(inputMessage).getCallBackInterface());
            }

            if (OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.CUSTOMS_REFUND.getCode()))) {
                // 自定义托寄物，修改报关方式需要退款
                try {
                    iDomainFlowEngine.pushCallBack(refundFlowNode.call(inputMessage).getCallBackInterface());
                } catch (Exception e) {
                    LOGGER.error("自定义托寄物-异步退款异常", e);
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REFUND_EXCEPTION_ALARM, "自定义托寄物-异步退款异常,traceId=" + context.getRequestProfile().getTraceId());
                    context.putUnblockedExceptionValue(refundFlowNode.getClass().getName(), e.getMessage());
                }
            }

            // 异步事后折询价（修改支持接口或者原单传入）
            if(OrderConstants.YES_VAL.equals(context.getOrderModel().getExtendProps().get(ContextInfoEnum.ADDITION_PRICE_FLAG.getCode()))
                    || OrderConstants.YES_VAL.equals(context.getOrderModel().getOrderSnapshot().getExtendProps().get(ContextInfoEnum.ADDITION_PRICE_FLAG.getCode()))) {
                LOGGER.info("加价策略标识不为空，异步事后折询价，执行开始");
                try {
                    iDomainFlowEngine.pushCallBack(asyncStandardProductAndDiscountEnquiryFlowNode.call(inputMessage).getCallBackInterface());
                } catch (Exception e) {
                    LOGGER.error("异步事后折询价异常", e);
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REFUND_EXCEPTION_ALARM, "异步事后折询价异常,traceId=" + context.getRequestProfile().getTraceId());
                    context.putUnblockedExceptionValue(refundFlowNode.getClass().getName(), e.getMessage());
                }
            }


            try {
                // 修改成功发送订单数据流水
                iDomainFlowEngine.pushCallBack(orderFieldDataNotifyFlowNode.call(inputMessage).getCallBackInterface());
            } catch (Exception e) {
                LOGGER.error("纯配修改领域能力-修改成功发送订单数据流水执行异常：", e);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ORDER_FIELD_DATA_NOTIFY_EXCEPTION_ALARM, "修改发送订单数据流水异常，orderNo=" + orderModel.orderNo());
            }

            try {
                // 修改发送全程跟踪
                iDomainFlowEngine.pushCallBack(orderTrackFlowNode.call(inputMessage).getCallBackInterface());
            } catch (Exception e) {
                LOGGER.error("纯配修改领域能力-修改发送全程跟踪执行异常：", e);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_MODIFY_SEND_ORDER_TRACK_EXCEPTION_ALARM, "修改发送全程跟踪异常，orderNo=" + orderModel.orderNo());
            }

            // fixme 改址一单到底额外信息赋值
            appendModifyExpressOrderResultInfo(modifyExpressOrderResult, context);

            try {
                // 揽收前修改 发送运单全程跟踪
                iDomainFlowEngine.pushCallBack(traceUploadFlowNode.call(inputMessage).getCallBackInterface());
            } catch (Exception e) {
                LOGGER.error("纯配修改领域能力-揽收前修改发送运单全程跟踪执行异常：", e);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_MODIFY_SEND_ORDER_TRACK_EXCEPTION_ALARM, "揽收前修改发送运单全程跟踪执行异常，orderNo=" + context.getOrderModel().orderNo());
            }

            // 补全返回结果
            modifyExpressOrderResult.setExtendProps(context.getExtendProps());
            return modifyExpressOrderResult;
        } catch (DomainAbilityException abilityException) {
            iDomainFlowEngine.callBack();
            LOGGER.error("纯配修改领域服务处理异常:traceId={},exception:", orderModel.traceId(), abilityException);
            throw abilityException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            iDomainFlowEngine.callBack();
            LOGGER.error("纯配修改领域服务处理异常:traceId={},exception:", orderModel.traceId(),exception);
            throw new DomainServiceException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR, exception);
        } finally {
            if (UnitedBusinessIdentityUtil.isUnitedIdentity(context)) {
                // 更新为真实身份继续执行流程
                UnitedBusinessIdentityUtil.convertToOriginal(context);
            }
            Profiler.registerInfoEnd(callerInfo);
            if (context.getLock() != null) {
                context.getLock().unlock();
            }
        }
    }

    /**
     * 追加响应信息
     * @param modifyExpressOrderResult 响应体
     * @param context 请求上下文
     */
    private void appendModifyExpressOrderResultInfo(ModifyExpressOrderResult modifyExpressOrderResult, ExpressOrderContext context){
        ExpressOrderModel orderModel = context.getOrderModel();
        PaymentStageEnum paymentStage = orderModel.getFinance().getPaymentStage();
        SettlementTypeEnum settlementType = orderModel.getFinance().getSettlementType();
        // 改址一单到底额外信息赋值
        if (orderModel.isReaddress1Order2End() || orderModel.isKKInterceptionThroughOrderRecord()) {
            FinanceInfo financeInfo = new FinanceInfo();
            ShipmentInfo shipmentInfo = new ShipmentInfo();
            // 改址一单到底赋值 支付时机
            if (null != paymentStage) {
                financeInfo.setPaymentStage(paymentStage.getCode());
            }
            if(null != settlementType){
                financeInfo.setSettlementType(settlementType.getCode());
            }
            // 改址先款 => 从改址记录取
            ModifyRecord lastThroughOrderModifyRecord = null;
            if(null != context.getModifyRecordDelegate()){
                lastThroughOrderModifyRecord = context.getModifyRecordDelegate().getLastThroughOrderModifyRecord();
            }
            if(null != lastThroughOrderModifyRecord){
                // 修改记录号
                modifyExpressOrderResult.setModifyRecordSequenceNo(lastThroughOrderModifyRecord.getModifyRecordNo());
            }
            if (PaymentStageEnum.ONLINEPAYMENT == paymentStage) {
                ReaddressRecordDetailInfo readdressRecordDetailInfo = null;
                // 先款单 支付截止时间 差额 预计送达时间 从修改记录取
                if (null != lastThroughOrderModifyRecord){
                    readdressRecordDetailInfo = (ReaddressRecordDetailInfo) lastThroughOrderModifyRecord.getModifyRecordDetail();
                }
                if (null != readdressRecordDetailInfo){
                    // 支付截止时间 + 差额
                    financeInfo.setPayDeadline(readdressRecordDetailInfo.getFinance().getPayDeadline());
                    financeInfo.setReceivableDifferenceAmount(readdressRecordDetailInfo.getFinance().getReceivableDifferenceAmount());
                    // 预计送达时间
                    shipmentInfo.setPlanDeliveryTime(readdressRecordDetailInfo.getShipment().getPlanDeliveryTime());
                }
            } else {
                // 后款单 没有差额 和 支付截止时间
                // 预计送达时间 从当前单取
                shipmentInfo.setPlanDeliveryTime(orderModel.getShipment().getPlanDeliveryTime());
            }
            String startStationNo = orderModel.getShipment().getStartStationNo();
            if (StringUtils.isBlank(startStationNo) && orderModel.getOrderSnapshot() != null && orderModel.getOrderSnapshot().getShipment() != null) {
                startStationNo = orderModel.getOrderSnapshot().getShipment().getStartStationNo();
            }
            shipmentInfo.setStartStationNo(startStationNo);

            String endStationNo = orderModel.getShipment().getEndStationNo();
            if (StringUtils.isBlank(endStationNo) && orderModel.getOrderSnapshot() != null && orderModel.getOrderSnapshot().getShipment() != null) {
                endStationNo = orderModel.getOrderSnapshot().getShipment().getEndStationNo();
            }
            shipmentInfo.setEndStationNo(endStationNo);

            modifyExpressOrderResult.setFinanceInfo(financeInfo);
            modifyExpressOrderResult.setShipmentInfo(shipmentInfo);

            // 待支付金额
            MoneyInfo pendingMoney = (MoneyInfo) context.getExtInfo(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode());
            modifyExpressOrderResult.setPendingPayment(pendingMoney);
        }
        // 港澳报关先款支付场景，写待支付金额
        Object extInfo = context.getExtInfo(ContextInfoEnum.CUSTOMS_ONLINE_PAY_PENDING_MONEY.getCode());
        if (extInfo instanceof MoneyInfo) {
            // 待支付金额
            modifyExpressOrderResult.setPendingPayment((MoneyInfo) extInfo);
        } else if (null != extInfo) {
            // TODO 自定义告警
            LOGGER.error("港澳报关费待支付金额异常: {}", JSONUtils.beanToJSONDefaultLazy(extInfo));
            umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_PENDING_MONEY_ALARM, "报关待支付金额异常", orderModel.orderNo());
        }
    }

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 纯配简易修改领域服务修改领域服务
     * @lastModify update by wangjingzhao 2021-06-05 21:29:40 增加batrix流程图外无自动回滚策略显示调用回滚策略
     */
    @Override
    public ModifyExpressOrderResult simpleModifyOrder(ExpressOrderContext context) throws DomainServiceException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".simpleModifyOrder"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        ModifyExpressOrderResult modifyExpressOrderResult = new ModifyExpressOrderResult();
        //初始化引擎
        BFlowEngine iDomainFlowEngine = new BFlowEngine.Builder()
                .code(FlowEngineConstants.JDL_CP_EXPRESS_ORDER_MODIFY_ENGINE_CODE)
                .name(FlowEngineConstants.JDL_CP_EXPRESS_ORDER_MODIFY_ENGINE_NAME)
                .domainCode(DomainConstants.EXPRESS_ORDER_DOMIAN_CODE).build();
        ExpressOrderModel orderModel = context.getOrderModel();
        try {
            LOGGER.info("纯配修改领域服务修改领域对象入参:context={}", JSONUtils.beanToJSONDefault(context));
            //流程解析数据
            BFlowInitConfig bFlowInitConfig = new BFlowInitConfig();
            bFlowInitConfig.setCode(FlowEngineConstants.JDL_CP_EXPRESS_ORDER_MODIFY_ENGINE_CODE);
            bFlowInitConfig.setName(FlowEngineConstants.JDL_CP_EXPRESS_ORDER_MODIFY_ENGINE_NAME);
            //流程编排
            BFlowContext flowContext = new BFlowContext(context);
            BOutputMessage bOutputMessage = iDomainFlowEngine.start(bFlowInitConfig, flowContext);
            if (null != bOutputMessage && bOutputMessage.isBlock() && bOutputMessage.isException()) {
                LOGGER.info("纯配修改领域修改服务流程引擎执行异常,需中断", bOutputMessage.getBException());
                throw bOutputMessage.getBException();
            }
            modifyExpressOrderResult.setCode(ModifyResultEnum.MODIFY_SUCCESS.getCode());
            LOGGER.info("纯配修改领域修改服务处理结束,出参, dooReceiveResult={}", JSONUtils.beanToJSONDefault(modifyExpressOrderResult));
            // 补全返回结果
            modifyExpressOrderResult.setExtendProps(context.getExtendProps());
            return modifyExpressOrderResult;
        } catch (DomainAbilityException abilityException) {
            iDomainFlowEngine.callBack();
            LOGGER.error("纯配修改领域服务处理异常:traceId={},exception:", orderModel.traceId(), abilityException);
            throw abilityException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            iDomainFlowEngine.callBack();
            LOGGER.error("纯配修改领域服务处理异常:traceId={},exception:", orderModel.traceId(),exception);
            throw new DomainServiceException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            if (context.getLock() != null) {
                context.getLock().unlock();
            }
        }
    }
}
