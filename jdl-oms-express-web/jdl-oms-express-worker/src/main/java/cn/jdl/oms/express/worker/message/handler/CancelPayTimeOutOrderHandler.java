package cn.jdl.oms.express.worker.message.handler;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CancelExpressOrderResult;
import cn.jdl.oms.express.domain.converter.BusinessIdentityMapper;
import cn.jdl.oms.express.domain.converter.ChannelMapper;
import cn.jdl.oms.express.domain.dto.BusinessIdentityDto;
import cn.jdl.oms.express.domain.dto.ChannelInfoDto;
import cn.jdl.oms.express.domain.dto.record.ModifyRecordDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.AllowCancelStatusFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.OrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.RetailOrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessIdentityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ChannelFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.QueryOrderBankResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.OTSLedgerUtil;
import cn.jdl.oms.express.domain.infrs.acl.pl.repository.OrderDataNotifyTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.track.OrderTrackFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.FinanceUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.handler.ExpressAbstractHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.BdWaybillUploadTraceDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderDataFlowDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderStatusNotifyDataDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderStatusNotifyMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.PayTimeoutCancelMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ReaddressStatusNoticeMessage;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisClient;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLockFactory;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ReaddressStatusNoticeMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.lock.LockEntry;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.service.impl.CancelExpressOrderDomainService;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ReaddressStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.utils.OrderDataFieldEnum;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.freight.extension.util.ReaddressStatusNoticeMessageBuilder;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.B2BBatchBusinessExpansionStatusEnum;
import cn.jdl.oms.express.shared.common.dict.B2BBatchCancelReasonCodeEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessTypeEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.CancelInterceptTypeEnum;
import cn.jdl.oms.express.shared.common.dict.CancelResultEnum;
import cn.jdl.oms.express.shared.common.dict.CancelTypeEnum;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyRecordListUpdateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyRecordUpdateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.OrderTrackEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusExtendEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.IgnoreDomainException;
import cn.jdl.oms.express.shared.common.exception.JMQRetryException;
import cn.jdl.oms.express.shared.common.exception.ValidationDomainException;
import cn.jdl.oms.express.shared.common.mdc.MDCTraceConstants;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.jsf.gd.util.Constants;
import com.jd.jsf.gd.util.RpcContext;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 监听消息队列MQ,三分钟后收到，超时自动取消
 */
public class CancelPayTimeOutOrderHandler extends ExpressAbstractHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(CancelPayTimeOutOrderHandler.class);

    /**
     * 取消拦截类型  订单超市取消只取消不拦截
     * 1：取消（不拦截）
     * <p>
     * 2：取消并拦截
     * <p>
     * 3：拦截
     */
    private static final Integer OPERATION_TYPE = 1;

    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;


    /**
     * 订单详情查询
     */
    @Resource
    private GetOrderFacade getOrderFacade;

    /**
     * 判断订单状态是否允许取消
     */
    @Resource
    private AllowCancelStatusFacade allowCancelStatusFacade;

    /**
     * 取消领域服务实现
     */
    @Resource
    private CancelExpressOrderDomainService cancelExpressOrderDomainService;
    /**
     * UCC配置
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /**
     * 修改防腐层
     */
    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    /**
     * 修改防腐层转换器
     */
    @Resource
    private ModifyOrderFacadeTranslator modifyOrderFacadeTranslator;

    /**
     * 操作类型key
     */
    public static final String OPERATE_TYPE_KEY = "operateType";

    /**
     * JMQ生产者：改址单状态通知
     */
    @Resource
    private JMQMessageProducer readdressStatusNoticeProducer;

    /**
     * PDQ改址单状态通知
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 状态广播
     */
    @Resource
    private JMQMessageProducer orderStatusNotifyJmqProducer;

    @Resource
    private FreightFTLConfirmTimeoutJmqHandler freightFTLConfirmTimeoutJmqHandler;

    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    @Resource
    private OrderTrackFacadeTranslator orderTrackFacadeTranslator;

    @Resource
    private IRedisLockFactory redisLockFactory;

    @Resource
    private IRedisClient redisClient;

    @Resource
    private RetailOrderBankFacade retailOrderBankFacade;

    /** 台账facade */
    @Resource
    private OrderBankFacade orderBankFacade;

    /**
     * 订单数据流水转换器
     */
    @Resource
    private OrderDataNotifyTranslator orderDataNotifyTranslator;

    /**
     * 订单超时取消
     *
     * @param commonDto
     * @return
     */
    @Override
    public boolean handle(CommonDto commonDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".handle"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        // 得到超时取消的消息体
        PayTimeoutCancelMessageDto payTimeoutCancelMessageDto = (PayTimeoutCancelMessageDto) commonDto;
        LOGGER.info("订单超时自动取消payTimeoutCancelMessageDto是 {} ", JSONUtils.beanToJSONDefault(payTimeoutCancelMessageDto));
        GetOrderFacadeResponse getOrderFacadeResponse = new GetOrderFacadeResponse();
        try {
            if (null == payTimeoutCancelMessageDto) {
                LOGGER.info("订单超时自动取消 ,场景业务数据对象不存在");
                return true;
            }

            // 获取订单详情
            getOrderFacadeResponse = getOrderFacade.getOrder(payTimeoutCancelMessageDto.getPayCancelRequestProfile(), convertGetOrderRequest(payTimeoutCancelMessageDto.getOrderNo()));
            if (getOrderFacadeResponse == null) {
                LOGGER.error("订单详情对象不存在 订单号是 {} ,需JMQ重试", payTimeoutCancelMessageDto.getOrderNo());
                throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL).withCustom("订单详情对象不存在需重试，订单号是 " + payTimeoutCancelMessageDto.getOrderNo());
            }
//            LOGGER.info("订单号是 {},订单详情是 {} ", payTimeoutCancelMessageDto.getOrderNo(), JSONUtils.beanToJSONDefault(getOrderFacadeResponse));

            //改址一单到底超时未支付
            if(StringUtils.isNotBlank(payTimeoutCancelMessageDto.getPayRecordNo())){
                LOGGER.info("改址一单到底超时未支付orderNo{},payRecordNo{} ", payTimeoutCancelMessageDto.getOrderNo(), payTimeoutCancelMessageDto.getPayRecordNo());
                handleReaddress1Order2EndCancelRecord(payTimeoutCancelMessageDto,getOrderFacadeResponse);
                return true;
            }

            // 冷链B2B整车超时未支付消息
            if (isCCB2BPayTimeout(payTimeoutCancelMessageDto)) {
                handleCCB2BBatchPayTimeout(payTimeoutCancelMessageDto, getOrderFacadeResponse);
                return true;
            }

            // 如果是快运整车
            if (isFreightFTLMessage(payTimeoutCancelMessageDto)) {
                // 处理B2C商家确认超时、C2C支付超时
                freightFTLConfirmTimeoutJmqHandler.handleFreightFTLTimeout(payTimeoutCancelMessageDto, getOrderFacadeResponse);
                return true;
            }

            // 如果是快运改址超时未支付消息
            if (isFreightReaddressMessage(payTimeoutCancelMessageDto, getOrderFacadeResponse)) {
                // 处理快运改址超时未支付
                handleFreightReaddress(payTimeoutCancelMessageDto, getOrderFacadeResponse);
                return true;
            }

            if (PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus().equals(getOrderFacadeResponse.getFinance().getPaymentStatus())) {
                LOGGER.info("支付完成无需取消订单");
                return true;
            }

            // 若支付状态不是支付完成 要比较的时间
            Date compareDate = DateUtils.now();
            // 订单原本的截止时间
            Date payDeadline = getOrderFacadeResponse.getFinance().getPayDeadline();
            if (payDeadline == null) {
                LOGGER.error("订单" + payTimeoutCancelMessageDto.getOrderNo() + "超时取消执行异常,订单原本支付截止时间为nulL");
                throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL).withCustom("超时取消执行异常,支付截止时间为nulL,JMQ重试,订单号是 " + payTimeoutCancelMessageDto.getOrderNo());
            }
            // 这个时间是订单原本支付截止时间 +buffer 后的截止时间 （留buffer缓冲操作  ucc 上 可配置 buffer 60s  ）
            Date payDeadlineWithBuffer = DateUtils.afterSomeSecondToCurrentDatetime(payDeadline, expressUccConfigCenter.getCancelPayTimeOutOrderBuffer());
            LOGGER.info("订单{}超时取消执行时间参数,payDeadline(无buffer)={},compareDate={},payDeadlineWithBuffer(有buffer)={}", payTimeoutCancelMessageDto.getOrderNo(), payDeadline, compareDate, payDeadlineWithBuffer);
            // 支付截止时间(带buffer后的时间)比当前时间早，说明当前时间已经过了支付截止时间，已经超时
            if (DateUtils.isBefore(payDeadlineWithBuffer, compareDate)) {
                // 已超时则执行取消
                doCancel(payTimeoutCancelMessageDto, getOrderFacadeResponse);
            } else {
                // 提前消费到消息就重试
                doRetry(payDeadline, payDeadlineWithBuffer, compareDate, payTimeoutCancelMessageDto);
            }
        } catch (BusinessDomainException e) {
            LOGGER.error("订单超时取消业务异常,传递的消息体是{},订单详情是{}", JSONUtils.beanToJSONDefault(payTimeoutCancelMessageDto), JSONUtils.beanToJSONDefault(getOrderFacadeResponse), e);

            LOGGER.error("订单超时取消业务异常 orderStatus 为{},orderStatusCustom 为 {}", OrderStatusEnum.of(getOrderFacadeResponse.getOrderStatus()),
                    ExpressOrderStatusCustomEnum.ofCustomOrderStatus(getOrderFacadeResponse.getOrderStatusCustom()));
            // 订单状态不允许取消的场景
            //业务重复取消场景 先款订单支付成功后取消失败 后款订单已下发后取消失败 认为不需要再取消
            if (StringUtils.equals(UnifiedErrorSpec.BasisOrder.REPEAT_CANCEL.code(), e.getDooErrorSpec().code())
                    || StringUtils.equals(UnifiedErrorSpec.BasisOrder.ONLINE_PAYMENT_CANCEL_FAIL.code(), e.getDooErrorSpec().code())
                    || StringUtils.equals(UnifiedErrorSpec.BasisOrder.CASHON_DELIVERY_CANCEL_FAIL.code(), e.getDooErrorSpec().code())) {
                LOGGER.error(e.getDooErrorSpec().desc() + "，认为消费成功,订单号是{}", payTimeoutCancelMessageDto.getOrderNo());
                return true;
            }
            //其他取消失败的场景 (ducc的问题或许是状态没同步，或许是其有配置，全部允许重试)
            LOGGER.error("订单超时取消业务异常,JMQ重试,订单号{}", payTimeoutCancelMessageDto.getOrderNo());
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL, e).withCustom("订单超时取消业务异常,JMQ重试,订单号是：" + payTimeoutCancelMessageDto.getOrderNo());

        } catch (IgnoreDomainException ignoreDomainException) {
            LOGGER.error("订单超时取消执行异常未到取消时间,预重试：{}", ignoreDomainException.fullMessage());
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL, ignoreDomainException).withCustom("订单超时取消执行异常未到取消时间需JMQ重试,订单号是：" + payTimeoutCancelMessageDto.getOrderNo());
//        TODO 代码内部抛出 JMQRetryException 应该识别
//        } catch (JMQRetryException jmqRetryException) {
//            LOGGER.error("订单超时取消执行异常未到取消时间,预重试,传递的消息体是{}", JSONUtils.beanToJSONDefault(payTimeoutCancelMessageDto), jmqRetryException);
//            throw jmqRetryException;
        } catch (Exception e) {
            LOGGER.error("订单超时取消执行异常,传递的消息体是{},订单详情是{}", JSONUtils.beanToJSONDefault(payTimeoutCancelMessageDto), JSONUtils.beanToJSONDefault(getOrderFacadeResponse), e);
            Profiler.functionError(callerInfo);
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL, e).withCustom("订单超时取消调度任务执行异常JMQ重试,订单号是：" + payTimeoutCancelMessageDto.getOrderNo());
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return true;
    }

    /**
     * 超时取消
     */
    private void doCancel(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse) {
        //根据订单详情得到上下文
        ExpressOrderContext expressOrderContext = expressOrderModelOf(payTimeoutCancelMessageDto.getPayCancelRequestProfile(), getOrderFacadeResponse);
        //调用取消接口
        CancelExpressOrderResult cancelExpressOrderResult = cancelExpressOrderDomainService.cancelOrder(expressOrderContext);
        //处理取消接口响应
        handleCancelExpressOrderResult(payTimeoutCancelMessageDto, getOrderFacadeResponse, cancelExpressOrderResult);

        BusinessIdentityFacade businessIdentity = getOrderFacadeResponse.getBusinessIdentity();
        if (null != businessIdentity) {
            if (BusinessTypeEnum.SERVICE_ENQUIRY.getCode().equals(businessIdentity.getBusinessType())) {
                //服务询价单超时未支付取消，需要发送超时未支付消息
                sendPayTimeOutStatusMessage(expressOrderContext);
            }
        }
    }

    /**
     * 处理调用取消接口的响应
     */
    private void handleCancelExpressOrderResult(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse, CancelExpressOrderResult cancelExpressOrderResult) {
        if (null == cancelExpressOrderResult) {
            LOGGER.error("订单{}超时取消执行异常,调用取消领域服务处理异常,cancelExpressOrderResult 是 null", payTimeoutCancelMessageDto.getOrderNo());
            //业务报警
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_CANCEL_PAY_TIME_OUT_ORDER_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "链路追踪ID:" + MDC.get(MDCTraceConstants.TRACEID)
                            + ","
                            + "申请租户:" + MDC.get(MDCTraceConstants.TRACEID)
                            + ","
                            + "订单"
                            + payTimeoutCancelMessageDto.getOrderNo()
                            + "超时取消失败原因: 调用cancelExpressOrderDomainService 返回值 cancelExpressOrderResult 为 null");
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL).withCustom("订单" + payTimeoutCancelMessageDto.getOrderNo()
                    + "超时取消调度任务执行异常");
        }
        LOGGER.info("订单{}超时自动取消结果是 {}", payTimeoutCancelMessageDto.getOrderNo(), JSONUtils.beanToJSONDefault(cancelExpressOrderResult));
        // 取消状态
        if (cancelExpressOrderResult.getCode() == CancelResultEnum.CANCEL_SUCCESS.getCode()) {
            LOGGER.info("订单{}状态取消成功，cancelExpressOrderResult {}", payTimeoutCancelMessageDto.getOrderNo(), JSONUtils.beanToJSONDefault(cancelExpressOrderResult));

        } else {
            LOGGER.error("订单{}超时取消执行异常,调用取消领域服务处理异常,cancelExpressOrderResult= {}", payTimeoutCancelMessageDto.getOrderNo(), JSONUtils.beanToJSONDefault(cancelExpressOrderResult));
            //业务报警
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_CANCEL_PAY_TIME_OUT_ORDER_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                            + ","
                            + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                            + ","
                            + "链路追踪ID:" + MDCTraceConstants.TRACEID
                            + ","
                            + "申请租户:" + MDCTraceConstants.TRACEID
                            + ","
                            + "订单"
                            + payTimeoutCancelMessageDto.getOrderNo()
                            + "超时取消失败原因: 调用取消领域服务处理异常,cancelExpressOrderResult = " + JSONUtils.beanToJSONDefault(cancelExpressOrderResult));
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL).withCustom("订单" + payTimeoutCancelMessageDto.getOrderNo() + "超时取消调度任务执行异常");
        }
    }

    /**
     * 重试
     */
    private void doRetry(Date payDeadline, Date payDeadlineWithBuffer, Date compareDate, PayTimeoutCancelMessageDto payTimeoutCancelMessageDto) {
        LOGGER.info("提前消费到消息，开始重试：当前订单号是{},订单原本支付截止时间(无buffer的时间)是 {},订单支付截止时间(有buffer的时间)是 {},比较的当前时间是 {} ,缓冲buffer时长是 {} 秒"
                , payTimeoutCancelMessageDto.getOrderNo()
                , payDeadline
                , payDeadlineWithBuffer, compareDate, expressUccConfigCenter.getCancelPayTimeOutOrderBuffer());
        // 提前消费到消息就重试
        throw new IgnoreDomainException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL).withCustom("订单" + payTimeoutCancelMessageDto.getOrderNo() + "提前消费到消息开始重试");
    }

    /**
     * channel 转换 ChannelInfo 暂时用不到
     *
     * @param channel
     * @return
     */
    private ChannelInfo toChannelInfo(ChannelFacade channel) {
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelNo(channel.getChannelNo());
        // 取消操作时间
        channelInfo.setChannelOperateTime(DateUtils.now());
        channelInfo.setSystemCaller(SystemCallerEnum.EXPRESS_OMS.getCode());
        channelInfo.setSystemSubCaller("取消超时");
        return channelInfo;
    }

    /**
     * 根据订单详情构建取消上下文
     *
     * @param profile
     * @param getOrderFacadeResponse
     * @return
     */
    private ExpressOrderContext expressOrderModelOf(RequestProfile profile, GetOrderFacadeResponse getOrderFacadeResponse) {

        if (null == profile) {
            LOGGER.error("初始化取消领域模型业务身份识别对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("参数校验失败");
        }

        ExpressOrderModel orderModel = toExpressOrderContext(profile, getOrderFacadeResponse);

        //默认垂直业务身份
        orderModel.setYId("JDL");
        //领域模型上线文
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), orderModel.requestProfile(),
                orderModel.getOrderBusinessIdentity().getBusinessScene());
        context.setOrderModel(orderModel);
        //初始化，下发履约执行层打标 目前仅支持下发一个履约层，下发时识别使用
        Set<String> promiseUnits = makingDispatcherHandler.execute(context);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //初始化，下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        // 支付方式
        context.getOrderModel().getFinance().setPayment(PaymentTypeEnum.of(getOrderFacadeResponse.getFinance().getPayment()));
        // 支付状态
        context.getOrderModel().getFinance().setPaymentStatus(PaymentStatusEnum.of(getOrderFacadeResponse.getFinance().getPaymentStatus()));
        //扩展状态-赋值为下单取消
        context.getOrderModel().setExecutedStatus(ExpressOrderStatusExtendEnum.XIA_DAN_QU_XIAO.getExtendStatus());

        // 快照里放一份同样的信息
        //  context.getOrderModel().assignSnapshot(context.getOrderModel());
        return context;
    }

    /**
     * profile  getOrderFacadeResponse 转 ExpressOrderModel
     *
     * @param profile
     * @param getOrderFacadeResponse
     * @return
     */
    private ExpressOrderModel toExpressOrderContext(RequestProfile profile, GetOrderFacadeResponse getOrderFacadeResponse) {

        //构造model creator对象
        ExpressOrderModelCreator creator = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse);
        //业务身份
        BusinessIdentityDto businessIdentity = new BusinessIdentityDto();
        businessIdentity.setBusinessUnit(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit());
        businessIdentity.setBusinessType(getOrderFacadeResponse.getBusinessIdentity().getBusinessType());
        businessIdentity.setBusinessScene(BusinessSceneEnum.CANCEL.getCode());

        creator.setBusinessIdentity(businessIdentity);

        Optional.ofNullable(getOrderFacadeResponse.getChannel()).ifPresent(channelInfo ->
                this.channelOf(creator, toChannelInfo(channelInfo)));
        //取消拦截类型
        creator.setCancelInterceptType(CancelInterceptTypeEnum.of(OPERATION_TYPE));
        //取消操作人
        creator.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        //业务场景
        creator.setBusinessScene(BusinessSceneEnum.CANCEL.getCode());
        //取消原因编码
        creator.setCancelReasonCode(String.valueOf(CancelTypeEnum.CT_1007.getCode()));
        //取消原因
        creator.setCancelReason(CancelTypeEnum.CT_1007.getDesc());
        // 构建model
        ExpressOrderModel originalModel = ExpressOrderModel.expressModelOf(creator);

        return originalModel.withRequestProfile(profile);
    }


    /**
     * 渠道数据对象转换成接单领域模型
     *
     * @param creator
     * @param channelInfo
     */
    private void channelOf(ExpressOrderModelCreator creator, ChannelInfo channelInfo) {
        //接受申请处理数据传输对象
        ChannelInfoDto channelInfoDto = new ChannelInfoDto();
        //渠道操作时间
        channelInfoDto.setChannelOperateTime(channelInfo.getChannelOperateTime());
        //取消来源，由订单中台统一分配
        channelInfoDto.setSystemCaller(SystemCallerEnum.of(channelInfo.getSystemCaller()));
        //取消子来源
        channelInfoDto.setSystemSubCaller(channelInfo.getSystemSubCaller());
        //领域模型对象防腐转换对象
        creator.setChannelInfo(channelInfoDto);
    }

    // 构造 GetOrderFacadeRequest
    private GetOrderFacadeRequest convertGetOrderRequest(String orderNo) {
        GetOrderFacadeRequest request = new GetOrderFacadeRequest();
        request.setOrderNo(orderNo);
        return request;
    }

    /**
     * 处理快运揽收后修改地址支付超时
     */
    private void handleFreightReaddress(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse) throws ParseException {
        // 消费到消息时如果已经不是改址中，则不处理（可能为改址成功-代表对账成功、改址失败-代表消息重复消费）
        if (getOrderFacadeResponse.getReaddressStatus() == null
                || !ReaddressStatusEnum.MODIFYING.getCode().equals(getOrderFacadeResponse.getReaddressStatus())) {
            LOGGER.info("订单号是{}，不是改址中，无须处理快运揽收后修改地址支付超时", payTimeoutCancelMessageDto.getOrderNo());
            return;
        }

        // 要比较的时间
        Date compareDate = DateUtils.now();
        // 订单原本的截止时间
        Date payDeadline = getOrderFacadeResponse.getFinance().getPayDeadline();
        if (payDeadline == null) {
            LOGGER.error("订单" + payTimeoutCancelMessageDto.getOrderNo() + "超时取消执行异常,订单原本支付截止时间为nulL");
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL).withCustom("超时取消执行异常,支付截止时间为nulL,JMQ重试,订单号是 " + payTimeoutCancelMessageDto.getOrderNo());
        }
        // 这个时间是订单原本支付截止时间 +buffer 后的截止时间 （留buffer缓冲操作  ucc 上 可配置 buffer 60s  ）
        Date payDeadlineWithBuffer = DateUtils.afterSomeSecondToCurrentDatetime(payDeadline, expressUccConfigCenter.getCancelPayTimeOutOrderBuffer());
        LOGGER.info("订单{}超时取消执行时间参数,payDeadline(无buffer)={},compareDate={},payDeadlineWithBuffer(有buffer)={}", payTimeoutCancelMessageDto.getOrderNo(), payDeadline, compareDate, payDeadlineWithBuffer);
        // 支付截止时间(带buffer后的时间)比当前时间早，说明当前时间已经过了支付截止时间，已经超时
        if (DateUtils.isBefore(payDeadlineWithBuffer, compareDate)) {
            // 已超时则执行取消
            doFreightCancel(payTimeoutCancelMessageDto, getOrderFacadeResponse);
        } else {
            // 提前消费到消息就重试
            doRetry(payDeadline, payDeadlineWithBuffer, compareDate, payTimeoutCancelMessageDto);
        }
    }

    /**
     * 快运揽收后修改地址支付超时，取消改址
     */
    private void doFreightCancel(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse) throws ParseException {
        LOGGER.info("订单号是{}，快运揽收后修改地址支付超时，取消改址", payTimeoutCancelMessageDto.getOrderNo());
        //根据订单详情得到上下文
        ExpressOrderContext expressOrderContext = toFreightExpressOrderContext(payTimeoutCancelMessageDto, getOrderFacadeResponse);
        //调用取消接口
        CancelExpressOrderResult cancelExpressOrderResult = cancelExpressOrderDomainService.cancelOrder(expressOrderContext);
        //处理取消接口响应
        handleCancelExpressOrderResult(payTimeoutCancelMessageDto, getOrderFacadeResponse, cancelExpressOrderResult);
        LOGGER.info("订单号是{}，快运揽收后修改地址支付超时，取消改址完成", payTimeoutCancelMessageDto.getOrderNo());
    }

    /**
     * 根据订单详情构建快运上下文
     */
    private ExpressOrderContext toFreightExpressOrderContext(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse) {
        RequestProfile profile = payTimeoutCancelMessageDto.getPayCancelRequestProfile();
        if (null == profile) {
            LOGGER.error("初始化取消领域模型业务身份识别对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("参数校验失败");
        }

        ExpressOrderModel orderModel = toFreightExpressOrderContext(profile, payTimeoutCancelMessageDto, getOrderFacadeResponse);

        orderModel.setYId("JDL");
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), orderModel.requestProfile(), orderModel.getOrderBusinessIdentity().getBusinessScene());
        context.setOrderModel(orderModel);
        Set<String> promiseUnits = makingDispatcherHandler.execute(context);
        Optional.ofNullable(promiseUnits).orElseThrow(() -> new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别"));
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));

        return context;
    }

    /**
     * 快运改址取消转为ExpressOrderModel
     */
    private ExpressOrderModel toFreightExpressOrderContext(RequestProfile profile, PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse) {
        //构造model creator对象
        ExpressOrderModelCreator creator = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse);
        //业务身份
        BusinessIdentityDto businessIdentity = new BusinessIdentityDto();
        businessIdentity.setBusinessUnit(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit());
        businessIdentity.setBusinessType(BusinessTypeEnum.READDRESS_TRANSPORT.getCode());
        businessIdentity.setBusinessScene(BusinessSceneEnum.CANCEL.getCode());
        creator.setBusinessIdentity(businessIdentity);

        Optional.ofNullable(getOrderFacadeResponse.getChannel()).ifPresent(channelInfo -> this.channelOf(creator, toFreightChannelInfo(channelInfo)));
        //取消拦截类型
        creator.setCancelInterceptType(CancelInterceptTypeEnum.of(OPERATION_TYPE));
        //取消操作人
        creator.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        //业务场景
        creator.setBusinessScene(BusinessSceneEnum.CANCEL.getCode());
        //取消原因编码
        creator.setCancelReasonCode(String.valueOf(CancelTypeEnum.CT_1007.getCode()));
        //取消原因
        creator.setCancelReason(CancelTypeEnum.CT_1007.getDesc());
        // 构建model
        ExpressOrderModel originalModel = ExpressOrderModel.expressModelOf(creator);

        return originalModel.withRequestProfile(profile);
    }

    /**
     * 快运改址取消转为ChannelInfo
     */
    private ChannelInfo toFreightChannelInfo(ChannelFacade channel) {
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelNo(channel.getChannelNo());
        channelInfo.setChannelOperateTime(DateUtils.now());
        channelInfo.setSystemCaller(SystemCallerEnum.EXPRESS_OMS.getCode());
        return channelInfo;
    }

    /**
     * 判断是否快运改址超时未支付消息
     */
    private boolean isFreightReaddressMessage(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse) {
        // 添加融合快运B2C判断
        return (isFreight(payTimeoutCancelMessageDto) || UnitedB2CUtil.isUnitedFreightB2C(getOrderFacadeResponse))
                && payTimeoutCancelMessageDto.getModifyConsignee() != null
                && payTimeoutCancelMessageDto.getModifyConsignee();
    }

    /**
     * 判断是否冷链B2B超时未支付消息
     */
    private boolean isCCB2BPayTimeout(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto) {
        return payTimeoutCancelMessageDto.getOrderBusinessIdentity() != null
                && BusinessUnitEnum.CN_JDL_CC_B2B.getCode().equals(payTimeoutCancelMessageDto.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 冷链B2B整车超时取消逻辑
     */
    private void handleCCB2BBatchPayTimeout(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse) throws ParseException {
        // 要比较的时间
        Date compareDate = DateUtils.now();
        // 订单原本的截止时间
        Date payDeadline = getOrderFacadeResponse.getFinance().getPayDeadline();
        if (payDeadline == null) {
            LOGGER.error("订单" + payTimeoutCancelMessageDto.getOrderNo() + "超时取消执行异常,订单原本支付截止时间为nulL");
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL).withCustom("超时取消执行异常,支付截止时间为nulL,JMQ重试,订单号是 " + payTimeoutCancelMessageDto.getOrderNo());
        }
        // 这个时间是订单原本支付截止时间 +buffer 后的截止时间 （留buffer缓冲操作  ucc 上 可配置 buffer 60s  ）
        Date payDeadlineWithBuffer = DateUtils.afterSomeSecondToCurrentDatetime(payDeadline, expressUccConfigCenter.getCcB2BCancelPayTimeOutOrderBuffer());
        LOGGER.info("订单{}超时取消执行时间参数,payDeadline(无buffer)={},compareDate={},payDeadlineWithBuffer(有buffer)={}", payTimeoutCancelMessageDto.getOrderNo(), payDeadline, compareDate, payDeadlineWithBuffer);
        // 支付截止时间(带buffer后的时间)比当前时间早，说明当前时间已经过了支付截止时间，已经超时
        if (DateUtils.isBefore(payDeadlineWithBuffer, compareDate)) {
            // 已超时则执行取消
            doCCB2BBatchCancel(payTimeoutCancelMessageDto, getOrderFacadeResponse);
        } else {
            // 提前消费到消息就重试
            doRetry(payDeadline, payDeadlineWithBuffer, compareDate, payTimeoutCancelMessageDto);
        }
    }

    /**
     * 冷链B2B整车超时取消
     */
    private void doCCB2BBatchCancel(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse) throws ParseException {
        LOGGER.info("订单号是{}，冷链整车支付超时取消", payTimeoutCancelMessageDto.getOrderNo());
        RequestProfile profile = payTimeoutCancelMessageDto.getPayCancelRequestProfile();
        if (null == profile) {
            LOGGER.error("初始化取消领域模型业务身份识别对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("参数校验失败");
        }
        // 若父单状态无效（已取消、已删除）则删除定时任务,有效则判断订单询价状态是否为已确认，若为已确认，则删除定时任务
        if (OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())
                || EnquiryStatusEnum.CONFIRMED.getCode().equals(getOrderFacadeResponse.getFinance().getEnquiryStatus())) {
            return;
        }

        ExpressOrderModel orderModel = toCCB2BExpressOrderContext(profile, getOrderFacadeResponse);
        orderModel.setYId("JDL");
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), orderModel.requestProfile(), orderModel.getOrderBusinessIdentity().getBusinessScene());
        context.setOrderModel(orderModel);
        Set<String> promiseUnits = makingDispatcherHandler.execute(context);
        Optional.ofNullable(promiseUnits).orElseThrow(() -> new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别"));
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));

        //调用取消接口
        CancelExpressOrderResult cancelExpressOrderResult = cancelExpressOrderDomainService.cancelOrder(context);
        //处理取消接口响应
        handleCancelExpressOrderResult(payTimeoutCancelMessageDto, getOrderFacadeResponse, cancelExpressOrderResult);
        //取消状态广播
        broadcastOrderStatus(context);
        //更新扩展状态
        try {
            Map<String, String> extendProps = orderModel.getExtendProps();
            extendProps.put(AttachmentKeyEnum.BUSINESS_EXPANSION_STATUS.getKey(), B2BBatchBusinessExpansionStatusEnum.CANCEL_TIMEOUT.getCode());
            extendProps.put(AttachmentKeyEnum.CANCEL_REASON_CODE.getKey(), B2BBatchCancelReasonCodeEnum.CANCEL_TIMEOUT.getCode());
            ModifyOrderFacadeRequest request = modifyOrderFacadeTranslator.toCCB2BBatchCancelModifyOrderFacadeRequest(context);
            modifyOrderFacade.modifyOrder(orderModel.requestProfile(), request);
        } catch (Exception e) {
            LOGGER.error("超时取消逻辑-更新询价单扩展状态失败", e);
        }
        LOGGER.info("订单号是{}，冷链整车支付超时取消", payTimeoutCancelMessageDto.getOrderNo());
    }

    /**
     * 快运改址取消转为ExpressOrderModel
     */
    private ExpressOrderModel toCCB2BExpressOrderContext(RequestProfile profile, GetOrderFacadeResponse getOrderFacadeResponse) {
        //构造model creator对象
        ExpressOrderModelCreator creator = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse);
        //业务身份
        BusinessIdentityDto businessIdentity = new BusinessIdentityDto();
        businessIdentity.setBusinessUnit(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit());
        businessIdentity.setBusinessType(BusinessTypeEnum.TRANSPORT.getCode());
        businessIdentity.setBusinessScene(BusinessSceneEnum.CANCEL.getCode());
        creator.setBusinessIdentity(businessIdentity);

        Optional.ofNullable(getOrderFacadeResponse.getChannel()).ifPresent(channelInfo -> this.channelOf(creator, toChannelInfo(channelInfo)));
        //取消拦截类型
        creator.setCancelInterceptType(CancelInterceptTypeEnum.of(OPERATION_TYPE));
        //取消操作人
        creator.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        //业务场景
        creator.setBusinessScene(BusinessSceneEnum.CANCEL.getCode());
        //取消原因
        creator.setCancelReason(CancelTypeEnum.CT_1007.getDesc());
        // 构建model
        ExpressOrderModel originalModel = ExpressOrderModel.expressModelOf(creator);

        return originalModel.withRequestProfile(profile);
    }

    /**
     * 订单状态广播
     */
    private void broadcastOrderStatus(ExpressOrderContext context) {
        //构建MQ消息体
        ExpressOrderModel orderModel = context.getOrderModel();
        OrderStatusNotifyDataDto dataDto = new OrderStatusNotifyDataDto();
        dataDto.setOrderNo(orderModel.orderNo());
        dataDto.setCustomOrderNo(orderModel.getCustomOrderNo());
        // 订单用途
        dataDto.setOrderUsage(GetFieldUtils.getOrderUsage(orderModel));
        // 订单自定义状态
        dataDto.setOrderStatus(orderModel.getCustomStatus());
        // 取消状态
        dataDto.setCancelStatus(orderModel.getCancelStatus().getCode());
        // 业务身份
        dataDto.setBusinessIdentity(BusinessIdentityMapper.INSTANCE.toBusinessIdentity(orderModel.getOrderBusinessIdentity()));
        // 渠道信息
        dataDto.setChannelInfo(ChannelMapper.INSTANCE.toChannelInfo(orderModel.getChannel()));
        // 配送信息
        ShipmentInfo shipmentInfo = new ShipmentInfo();
        shipmentInfo.setShipperNo(orderModel.getShipment().getShipperNo());
        shipmentInfo.setShipperName(orderModel.getShipment().getShipperName());
        dataDto.setShipmentInfo(shipmentInfo);
        // 操作时间
        dataDto.setOperateTime(orderModel.getOperateTime());

        OrderStatusNotifyMessageDto messageDto = new OrderStatusNotifyMessageDto();
        messageDto.setProfile(context.getRequestProfile());
        messageDto.setData(dataDto);
        try {
            String message = JSONUtils.beanToJSONDefault(messageDto);
            LOGGER.info("订单:{}, 发送取消状态广播，消息报文:{}", orderModel.orderNo(), message);
            Map<String, String> attributes = new HashMap<>();
            attributes.put("businessUnit", messageDto.getData().getBusinessIdentity().getBusinessUnit());
            orderStatusNotifyJmqProducer.send(orderModel.orderNo(), message, null);
            LOGGER.info("订单:{}, 发送取消状态广播成功", orderModel.orderNo());
        } catch (Exception e) {
            LOGGER.error("状态变更通知发送失败", e);
        }
    }

    /**
     * 发送超时未支付状态通知消息，
     * 1：改址失败：超时未支付
     * 2：服务询价单：超时未支付
     * todo 本MQ只是快运切量期间用来同步告知ECLP系统，超时未支付的状态变更，待ECLP列表查询切到订单中心后，此MQ消息需要下线
     */
    private void sendPayTimeOutStatusMessage(ExpressOrderContext expressOrderContext) {
        // 改址失败：超时未支付
        ReaddressStatusNoticeMessage message = new ReaddressStatusNoticeMessage();
        message.setRequestProfile(expressOrderContext.getRequestProfile());
        ReaddressStatusNoticeMessage.MessageData data = new ReaddressStatusNoticeMessage.MessageData();
        data.setBusinessIdentity(expressOrderContext.getOrderModel().getBusinessIdentity());
        data.setOrderNo(expressOrderContext.getOrderModel().orderNo());
        data.setCustomOrderNo(expressOrderContext.getOrderModel().getCustomOrderNo());
        data.setReaddressStatus(ReaddressStatusEnum.MODIFY_FAIL.getCode());
        data.setOperateTime(new Date());
        message.setData(data);

        LOGGER.info("快运发送超时未支付状态通知消息：开始");
        try {
            LOGGER.info("快运发送超时未支付状态通知消息：orderNo:{},消息内容:{}", expressOrderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(message));
            readdressStatusNoticeProducer.send(expressOrderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(message), null);
            LOGGER.info("快运发送超时未支付状态通知消息：成功");
        } catch (Exception e) {
            LOGGER.error("快运发送超时未支付状态通知消息：失败！message:{}", JSONUtils.beanToJSONDefault(message), e);
            // PDQ重试发送改址单状态通知消息
            sendPayTimeOutStatusMessagePDQ(message);
        }
        LOGGER.info("快运发送超时未支付状态通知消息：完成");
    }

    /**
     * PDQ重试快运发送超时未支付状态通知消息
     */
    private void sendPayTimeOutStatusMessagePDQ(ReaddressStatusNoticeMessage message) {
        LOGGER.info("运发送超时未支付状态通知消息失败，PDQ重试，开始");
        try {
            ReaddressStatusNoticeMessageDto dto = ReaddressStatusNoticeMessageBuilder.toReaddressStatusNoticeMessageDto(message);
            SchedulerMessage scheduler = new SchedulerMessage();
            scheduler.setDtoJson(JSONUtils.beanToJSONDefault(dto));
            scheduler.setDtoClass(ReaddressStatusNoticeMessageDto.class);
            schedulerService.addSchedulerTask(PDQTopicEnum.READDRESS_STATUS_NOTICE_RETRY, scheduler, FlowConstants.EXPRESS_ORDER_MODIFY_REPOSITORY_FLOW_CODE);
        } catch (Exception e) {
            LOGGER.error("运发送超时未支付状态通知消息，PDQ重试，失败", e);
        }
        LOGGER.info("运发送超时未支付状态通知消息，PDQ重试，结束");
    }

    /**
     * 判断业务身份是否快运
     */
    private boolean isFreight(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto) {
        if (payTimeoutCancelMessageDto == null) {
            return false;
        }
        OrderBusinessIdentity orderBusinessIdentity = payTimeoutCancelMessageDto.getOrderBusinessIdentity();
        if (orderBusinessIdentity == null) {
            return false;
        }
        String businessUnit = orderBusinessIdentity.getBusinessUnit();
        return BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.getCode().equals(businessUnit)
                || BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(businessUnit);
    }

    /**
     * 判断是否快运整车直达消息
     */
    private boolean isFreightFTLMessage(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto) {
        if (payTimeoutCancelMessageDto == null) {
            return false;
        }
        OrderBusinessIdentity orderBusinessIdentity = payTimeoutCancelMessageDto.getOrderBusinessIdentity();
        if (orderBusinessIdentity == null) {
            return false;
        }
        String businessUnit = orderBusinessIdentity.getBusinessUnit();
        String businessType = orderBusinessIdentity.getBusinessType();
        return BusinessTypeEnum.FTL_DIRECT.getCode().equals(businessType)
                && (BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.getCode().equals(businessUnit)
                || BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(businessUnit));
    }


    /**
     * 处理快运揽收后修改地址支付超时
     */
    private void handleReaddress1Order2EndCancelRecord(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse){
        LOGGER.info("改址一单到底-超时未支付处理，orderNo:{}", payTimeoutCancelMessageDto.getOrderNo());
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".handleReaddress1Order2EndCancelRecord"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try{
            // 消费到消息时如果已经不是改址中，则不处理（可能为改址成功-代表对账成功、改址失败-代表消息重复消费）
            if (getOrderFacadeResponse.getReaddressStatus() == null
                    || !ReaddressStatusEnum.MODIFYING.getCode().equals(getOrderFacadeResponse.getReaddressStatus())) {
                LOGGER.info("订单号:{}，改址记录:{}，不是改址中，无须处理改址一单到底支付超时", payTimeoutCancelMessageDto.getOrderNo(), payTimeoutCancelMessageDto.getPayRecordNo());
                return;
            }

            // 要比较的时间
            Date compareDate = DateUtils.now();
            // 改一单到底的截止时间
            Date payDeadline = payTimeoutCancelMessageDto.getPayDeadline();
            if (payDeadline == null) {
                LOGGER.error("订单" + payTimeoutCancelMessageDto.getOrderNo() + "超时取消执行异常,订单原本支付截止时间为nulL");
                throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.ORDER_TIME_OUT_CANCEL_FAIL).withCustom("超时取消执行异常,支付截止时间为nulL,JMQ重试,订单号是 " + payTimeoutCancelMessageDto.getOrderNo());
            }
            // 这个时间是订单原本支付截止时间 +buffer 后的截止时间 （留buffer缓冲操作  ucc 上 可配置 buffer 60s  ）
            Date payDeadlineWithBuffer = DateUtils.afterSomeSecondToCurrentDatetime(payDeadline, expressUccConfigCenter.getCancelPayTimeOutOrderBuffer());
            LOGGER.info("订单{}超时取消执行时间参数,payDeadline(无buffer)={},compareDate={},payDeadlineWithBuffer(有buffer)={}", payTimeoutCancelMessageDto.getOrderNo(), payDeadline, compareDate, payDeadlineWithBuffer);
            // 支付截止时间(带buffer后的时间)比当前时间早，说明当前时间已经过了支付截止时间，已经超时
            if (DateUtils.isBefore(payDeadlineWithBuffer, compareDate)) {
                // 已超时则执行取消
                doRecordDelete(payTimeoutCancelMessageDto, getOrderFacadeResponse);
            } else {
                // 提前消费到消息就重试
                doRetry(payDeadline, payDeadlineWithBuffer, compareDate, payTimeoutCancelMessageDto);
            }
        } catch (Exception e) {
            LOGGER.error("改址一单到底，超时未支付，取消处理，未知异常", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            LOGGER.info("改址一单到底-超时未支付，取消处理-结束，orderNo:{}", payTimeoutCancelMessageDto.getOrderNo());
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private void doRecordDelete(PayTimeoutCancelMessageDto payTimeoutCancelMessageDto, GetOrderFacadeResponse getOrderFacadeResponse){
        LOGGER.info("doRecordDelete的订单:{}，变更记录:{}", payTimeoutCancelMessageDto.getOrderNo(),payTimeoutCancelMessageDto.getPayRecordNo());
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse);
        ExpressOrderModel order = new ExpressOrderModel(orderModelCreator);
        ExpressOrderModelCreator snapshotCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse);
        ExpressOrderModel snapshot = new ExpressOrderModel(snapshotCreator);
        order.assignSnapshot(snapshot);
        String payRecordNo = payTimeoutCancelMessageDto.getPayRecordNo();
        GetOrderFacadeRequest getOrderFacadeRequest= new GetOrderFacadeRequest();
        getOrderFacadeRequest.setModifyRecordNo(payRecordNo);
        List<ModifyRecordDto> modifyRecordDtoList = new ArrayList<>();
        ModifyRecordDto modifyRecordDto = getOrderFacade.getOrderModifyInfo(payTimeoutCancelMessageDto.getPayCancelRequestProfile(), getOrderFacadeRequest);
        //todo 判断记录的支付状态，如果已支付的则不再处理
        String modifyRecordMsg = modifyRecordDto.getModifyRecordMsg();
        ReaddressRecordDetailInfo readdressRecordDetailInfo = JSONUtils.jsonToBean(modifyRecordMsg, ReaddressRecordDetailInfo.class);
        if(null != readdressRecordDetailInfo
                && null != readdressRecordDetailInfo.getFinance()
                && PaymentStatusEnum.COMPLETE_PAYMENT == PaymentStatusEnum.of(readdressRecordDetailInfo.getFinance().getPaymentStatus())){
            LOGGER.warn("订单超时取消需要处理的订单:{}变更记录:{},已经支付完成，无需取消", modifyRecordDto.getOrderNo(),modifyRecordDto.getModifyRecordNo());
            return;
        }

        String merchantId = MerchantEnum.ONLINE_READDRESS.getMerchantId();

        if(order.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(order)){
            merchantId = MerchantEnum.FREIGHT_READDRESS.getMerchantId();
        }
        // 查外单台账实收
        QueryOrderBankResponse queryOrderBankResponse = retailOrderBankFacade.queryOrderBank(payTimeoutCancelMessageDto.getPayRecordNo(), merchantId, "");
        // 查询外单台账实收，如果已经有实收，说明已经对账成功，不需要处理超时支付
        if (null != queryOrderBankResponse.getRealPayPrice() && queryOrderBankResponse.getRealPayPrice().compareTo(BigDecimal.ZERO) > 0) {
            LOGGER.info("订单号:{}，改址记录:{}，实收:{}, 已经支付成功，无须处理改址一单到底支付超时",
                    payTimeoutCancelMessageDto.getOrderNo(), payTimeoutCancelMessageDto.getPayRecordNo(), queryOrderBankResponse.getRealPayPrice());
            return;
        }

        //todo 改址记录超时未支付，将外单台账置为0，台账后续操作：
        String finalMerchantId = merchantId;
        BatrixSwitch.applyDefNotExecute(BatrixSwitchKey.READDRESS_RECORD_CANCEL_CLEAR_OTS_SWITCH, (a) -> {
            OrderBankFacadeRequest orderBankFacadeRequest = new OrderBankFacadeRequest();
            OrderBankFacadeRequest.OtsCancel otsCancel = new OrderBankFacadeRequest.OtsCancel();
            otsCancel.setMerchantId(finalMerchantId);
            otsCancel.setOrderType(OTSLedgerUtil.ORDER_TYPE);
            otsCancel.setVer(OTSLedgerUtil.OTS_DEFAULT_VER);
            orderBankFacadeRequest.setOtsCancel(otsCancel);
            orderBankFacadeRequest.setWaybillNo(payTimeoutCancelMessageDto.getPayRecordNo());
            orderBankFacade.clear(orderBankFacadeRequest);
            LOGGER.info("订单超时取消-改址记录清除外单台账应收-处理成功,订单:{}变更记录:{}", modifyRecordDto.getOrderNo(), modifyRecordDto.getModifyRecordNo());
            //todo 自定义告警 通知业务BP联系外单台账是否审核退款
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_READDRESS_RECORD_CANCEL_ALARM, "超时未支付，取消外单台账成功，通知业务BP联系外单台账审核退款，record:" + modifyRecordDto.getModifyRecordNo());
        });

        LOGGER.info("订单超时取消需要处理的订单:{}变更记录:{}", modifyRecordDto.getOrderNo(),modifyRecordDto.getModifyRecordNo());
        modifyRecordDto.setModifyInfoUpdateType(ModifyRecordUpdateTypeEnum.DELETE.getCode());
        modifyRecordDtoList.add(modifyRecordDto);
        if(modifyRecordDto.getModifyRecordSequence() == 1){
            ModifyRecordDto originModifyRecord = getOrderFacadeResponse.getModifyRecordDtos().stream().filter(recordDto -> {
                return recordDto.getModifyRecordSequence() == 0;
            }).findFirst().orElse(null);
            if(null != originModifyRecord){
                LOGGER.info("订单超时取消需要处理的订单:{}变更的原始记录:{}", originModifyRecord.getOrderNo(),originModifyRecord.getModifyRecordNo());
                originModifyRecord.setModifyInfoUpdateType(ModifyRecordUpdateTypeEnum.DELETE.getCode());
                modifyRecordDtoList.add(originModifyRecord);
            }
        }
        order.setReaddressStatus(this,ReaddressStatusEnum.MODIFY_FAIL);
        String tenantId = payTimeoutCancelMessageDto.getPayCancelRequestProfile().getTenantId();
        // 加锁 readdress_pay_result_lock_订单号
        IRedisLock redisLock = null;
        try {
            String lockKey = new StringBuilder(OrderConstants.READDRESS_PAY_RESULT_LOCK_PREFIX).append(modifyRecordDto.getModifyRecordNo()).toString();
            LockEntry lockEntry = new LockEntry(lockKey, 5, TimeUnit.SECONDS);
            redisLock = redisLockFactory.create(redisClient, lockEntry);
            LOGGER.info("改址一单到底支付结果并发处理-支付超时-KEY：{}", lockKey);
            if (!redisLock.tryLock()) {
                // 改址一单到底支付结果并发处理，锁冲突
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.REPEAT_SUBMIT)
                        .withCustom("改址一单到底支付结果并发处理-支付超时，锁冲突");
            }

            //持久化处理，清除改址记录，订单改址状态处理为改址失败
            ModifyOrderFacadeRequest request = null;
            try {
                //京东转德邦，计算多方计费总金额
                if (order.isJDLToDPDelivery()) {
                    //获取原单改址记录，并剔除需要删除的改址记录
                    List<ModifyRecord> needComputeRecords = new ArrayList<>();
                    for (ModifyRecord modifyRecord : order.getModifyRecordDelegate().getEnabledModifyRecords()) {
                        if (modifyRecordDtoList.stream().noneMatch(record -> record.getModifyRecordNo().equals(modifyRecord.getModifyRecordNo()))) {
                            needComputeRecords.add(modifyRecord);
                        }
                    }
                    List<CostInfo> multiPartiesTotalAmounts = FinanceUtil.computeMPTotalAmountsGroupSettleAndStage(order, null, needComputeRecords, false, false);
                    order.complement().complementMultiPartiesTotalAmounts(this, multiPartiesTotalAmounts);
                }

                request = modifyOrderFacadeTranslator.toReaddress1Order2EndModifyOrderRequest(tenantId,order,modifyRecordDtoList, ModifyRecordListUpdateTypeEnum.INCREMENTAL_UPDATE.getCode());
                modifyOrderFacade.modifyOrder(payTimeoutCancelMessageDto.getPayCancelRequestProfile(), request);
                LOGGER.info("订单超时取消-持久化处理，清除改址记录成功,订单:{}变更记录:{}", modifyRecordDto.getOrderNo(), modifyRecordDto.getModifyRecordNo());
            } catch (Exception e){
                LOGGER.error("单持久化防腐层异常", e);
                //触发重试
                produceRetryMq(payTimeoutCancelMessageDto.getPayCancelRequestProfile(),request);
            }

            // 改址一单到底-发送全程跟踪
            if (StringUtils.isNotBlank(payTimeoutCancelMessageDto.getPayRecordNo())) {
                BdWaybillUploadTraceDto bdWaybillUploadTraceDto = orderTrackFacadeTranslator.toWaybillUploadTraceDto(getOrderFacadeResponse, OrderTrackEnum.READDRESS_FAILED_TIMEOUT, null);
                expressOrderFlowService.sendOrderTrackMq(bdWaybillUploadTraceDto);
                LOGGER.info("异步发送全程跟踪-扩展能力-发送mq消息，详细内容：{}", JSONUtils.beanToJSONDefault(bdWaybillUploadTraceDto));
            }

            try {
                if (CollectionUtils.isNotEmpty(order.getFinance().getMultiPartiesTotalAmounts())) {
                    LOGGER.info("发送多方计费金额变更通知");
                    //发送多方计费金额变更通知
                    OrderDataFlowDto dto = orderDataNotifyTranslator.toGeneralOrderDataFlowDto(order, OrderConstants.NO_VAL, OrderDataFieldEnum.MULTI_PARTIES_TOTAL_AMOUNTS);
                    if (null != dto) {
                        expressOrderFlowService.sendOrderDataRecordMq(dto);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("发送多方计费金额变更通知异常", e);
                //TODO 自定义告警
            }
            LOGGER.info("订单超时取消处理成功,订单:{}变更记录:{}", modifyRecordDto.getOrderNo(),modifyRecordDto.getModifyRecordNo());

        } catch (Exception e) {
            LOGGER.error("超时未支付", e);
            throw e;
        } finally {
            if (null != redisLock) {
                redisLock.unlock();
            }
        }
    }

    /**
     * 持久化异常处理
     */
    private void produceRetryMq(RequestProfile requestProfile, ModifyOrderFacadeRequest modifyOrderFacadeRequest) {
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        //持久化消息
        ModifyRepositoryMessageDto messageDto = new ModifyRepositoryMessageDto();
        messageDto.setRequestProfile(requestProfile);
        messageDto.setModifyOrderFacadeRequest(modifyOrderFacadeRequest);

        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(messageDto));
        schedulerMessage.setDtoClass(ModifyRepositoryMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.MODIFY_REPOSITORY_RETRY, schedulerMessage,
                FlowConstants.EXPRESS_ORDER_MODIFY_REPOSITORY_FLOW_CODE);
    }
}
