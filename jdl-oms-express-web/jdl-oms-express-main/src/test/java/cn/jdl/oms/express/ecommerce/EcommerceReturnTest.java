package cn.jdl.oms.express.ecommerce;

import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.shared.common.dict.ECardDisableReasonEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeTranslator;
import cn.jdl.oms.express.horz.ext.white.ModifyWhiteExtension;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.WaybillInfoMappingUtil;
import cn.jdl.oms.express.domain.spec.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.model.ChangedPropertyDelegate;
import cn.jdl.oms.express.domain.spec.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.util.MarkUtil;
import cn.jdl.oms.express.domain.spec.message.pl.waybill.ProductInfoDto;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.junit.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 电商退货特惠功能单元测试
 * 
 * <AUTHOR>
 * @date 2025-09-11
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class EcommerceReturnTest {

    @Resource
    private OrderBankFacadeTranslator orderBankFacadeTranslator;

    @Resource
    private ModifyWhiteExtension modifyWhiteExtension;

    @Resource
    private WaybillInfoMappingUtil waybillInfoMappingUtil;

    /**
     * 测试电商退货产品枚举定义
     */
    @Test
    public void testEcommerceReturnProductEnum() {
        ProductEnum product = ProductEnum.ECOMMERCE_RETURN;
        
        // 验证产品代码
        assertEquals("ed-m-0076", product.getCode());
        
        // 验证产品名称
        assertEquals("电商退货", product.getDesc());
        
        // 验证标记位索引
        assertEquals(Integer.valueOf(31), product.getSignIndex());
        
        // 验证标记位值
        assertEquals("N", product.getSign());
    }

    /**
     * 测试E卡支付限制枚举
     */
    @Test
    public void testECardDisableReasonEnum() {
        ECardDisableReasonEnum reason = ECardDisableReasonEnum.ECOMMERCE_RETURN;
        
        // 验证错误码
        assertEquals("3007", reason.getCode());
        
        // 验证错误描述
        assertEquals("电商退货产品不支持E卡支付", reason.getDesc());
    }

    /**
     * 测试电商退货产品E卡支付限制
     */
    @Test
    public void testEcommerceReturnECardPaymentRestriction() {
        // 创建电商退货订单模型
        ExpressOrderModel orderModel = createTestOrderModel();
        orderModel.setMajorProductNo(ProductEnum.ECOMMERCE_RETURN.getCode());
        
        // 测试E卡支付限制
        ECardDisableReasonEnum result = orderBankFacadeTranslator.isDisableECard(orderModel);
        
        // 验证返回电商退货支付限制
        assertEquals(ECardDisableReasonEnum.ECOMMERCE_RETURN, result);
    }

    /**
     * 测试非电商退货产品不受E卡支付限制
     */
    @Test
    public void testNonEcommerceReturnECardPaymentAllowed() {
        // 创建特惠送订单模型
        ExpressOrderModel orderModel = createTestOrderModel();
        orderModel.setMajorProductNo(ProductEnum.THS.getCode());
        
        // 测试E卡支付限制（应该通过其他检查逻辑）
        ECardDisableReasonEnum result = orderBankFacadeTranslator.isDisableECard(orderModel);
        
        // 验证不是因为电商退货产品被限制
        assertNotEquals(ECardDisableReasonEnum.ECOMMERCE_RETURN, result);
    }

    /**
     * 测试电商退货产品在运单映射中的处理
     */
    @Test
    public void testEcommerceReturnWaybillMapping() {
        // 创建标记位工具，设置电商退货产品标记
        String markString = "30001000010900030000000020008000000020000002002000002000010000000000001000000010000001000010100000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000";
        MarkUtil markUtil = new MarkUtil(markString);
        
        // 设置第31位为'N'（电商退货产品标记）
        markUtil.inChar(31, 'N');
        
        // 调用运单映射方法
        Map<String, ProductInfoDto> result = waybillInfoMappingUtil.buildProductInfo(markUtil);
        
        // 验证主产品映射正确
        assertTrue(result.containsKey(ProductEnum.ECOMMERCE_RETURN.getCode()));
        ProductInfoDto mainProduct = result.get(ProductEnum.ECOMMERCE_RETURN.getCode());
        assertEquals(ProductEnum.ECOMMERCE_RETURN.getCode(), mainProduct.getProductNo());
        assertEquals(ProductEnum.ECOMMERCE_RETURN.getDesc(), mainProduct.getProductName());
    }

    /**
     * 创建测试用的订单模型
     */
    private ExpressOrderModel createTestOrderModel() {
        ExpressOrderModel orderModel = new ExpressOrderModel();
        orderModel.setOrderNo("EO0020040892960");
        orderModel.setBusinessIdentity("C2C");
        
        // 创建订单快照
        ExpressOrderModel snapshot = new ExpressOrderModel();
        snapshot.setOrderStatus(OrderStatusEnum.WAIT_ACCEPT);
        orderModel.setOrderSnapshot(snapshot);
        
        return orderModel;
    }

    /**
     * 创建测试用的属性变更委托
     */
    private ChangedPropertyDelegate createChangedPropertyDelegate() {
        ChangedPropertyDelegate delegate = new ChangedPropertyDelegate();
        return delegate;
    }
}
