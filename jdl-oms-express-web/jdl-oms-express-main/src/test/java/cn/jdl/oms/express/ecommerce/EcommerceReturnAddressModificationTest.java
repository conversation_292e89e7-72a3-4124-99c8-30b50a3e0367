package cn.jdl.oms.express.ecommerce;

import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.horz.ext.white.ModifyWhiteExtension;
import cn.jdl.oms.express.domain.spec.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.model.OrderSnapshot;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.junit.Assert;

import javax.annotation.Resource;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 电商退货产品地址修改限制功能单元测试
 * 
 * <AUTHOR>
 * @date 2025-09-11
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class EcommerceReturnAddressModificationTest {

    @Resource
    private ModifyWhiteExtension modifyWhiteExtension;

    /**
     * 测试电商退货产品揽收前修改省份地址被限制
     */
    @Test(expected = BusinessDomainException.class)
    public void testEcommerceReturnProvinceModificationBeforePickupRestricted() {
        // 创建电商退货订单（C2C业务，揽收前状态）
        ExpressOrderModel orderModel = createEcommerceReturnOrder(OrderStatusEnum.WAIT_ACCEPT);
        
        // 创建省份地址变更
        ChangedPropertyDelegate changedPropertyDelegate = createProvinceChangedDelegate();
        
        // 创建订单上下文
        ExpressOrderContext context = new ExpressOrderContext();
        context.setOrderModel(orderModel);
        context.setChangedPropertyDelegate(changedPropertyDelegate);
        
        // 执行修改校验，应该抛出BusinessDomainException
        modifyWhiteExtension.execute(context);
    }

    /**
     * 测试电商退货产品揽收前修改城市地址被限制
     */
    @Test(expected = BusinessDomainException.class)
    public void testEcommerceReturnCityModificationBeforePickupRestricted() {
        // 创建电商退货订单（C2C业务，揽收前状态）
        ExpressOrderModel orderModel = createEcommerceReturnOrder(OrderStatusEnum.WAIT_ACCEPT);
        
        // 创建城市地址变更
        ChangedPropertyDelegate changedPropertyDelegate = createCityChangedDelegate();
        
        // 创建订单上下文
        ExpressOrderContext context = new ExpressOrderContext();
        context.setOrderModel(orderModel);
        context.setChangedPropertyDelegate(changedPropertyDelegate);
        
        // 执行修改校验，应该抛出BusinessDomainException
        modifyWhiteExtension.execute(context);
    }

    /**
     * 测试电商退货产品揽收前修改县区地址被限制
     */
    @Test(expected = BusinessDomainException.class)
    public void testEcommerceReturnCountyModificationBeforePickupRestricted() {
        // 创建电商退货订单（C2C业务，揽收前状态）
        ExpressOrderModel orderModel = createEcommerceReturnOrder(OrderStatusEnum.WAIT_ACCEPT);
        
        // 创建县区地址变更
        ChangedPropertyDelegate changedPropertyDelegate = createCountyChangedDelegate();
        
        // 创建订单上下文
        ExpressOrderContext context = new ExpressOrderContext();
        context.setOrderModel(orderModel);
        context.setChangedPropertyDelegate(changedPropertyDelegate);
        
        // 执行修改校验，应该抛出BusinessDomainException
        modifyWhiteExtension.execute(context);
    }

    /**
     * 测试电商退货产品揽收后修改地址允许
     */
    @Test
    public void testEcommerceReturnAddressModificationAfterPickupAllowed() {
        // 创建电商退货订单（C2C业务，揽收后状态）
        ExpressOrderModel orderModel = createEcommerceReturnOrder(OrderStatusEnum.PICKED_UP);
        
        // 创建地址变更
        ChangedPropertyDelegate changedPropertyDelegate = createProvinceChangedDelegate();
        
        // 创建订单上下文
        ExpressOrderContext context = new ExpressOrderContext();
        context.setOrderModel(orderModel);
        context.setChangedPropertyDelegate(changedPropertyDelegate);
        
        // 执行修改校验，不应该抛出异常
        try {
            modifyWhiteExtension.execute(context);
            // 如果没有抛出异常，测试通过
        } catch (BusinessDomainException e) {
            // 如果抛出的异常不是地址修改限制相关的，则忽略
            if (!e.getMessage().contains("电商退货产品揽收前不允许修改省市区地址")) {
                // 其他业务异常，测试通过
            } else {
                fail("揽收后不应该限制地址修改");
            }
        }
    }

    /**
     * 测试非电商退货产品不受地址修改限制
     */
    @Test
    public void testNonEcommerceReturnProductNotRestricted() {
        // 创建特惠送订单（C2C业务，揽收前状态）
        ExpressOrderModel orderModel = createNonEcommerceReturnOrder(OrderStatusEnum.WAIT_ACCEPT);
        
        // 创建地址变更
        ChangedPropertyDelegate changedPropertyDelegate = createProvinceChangedDelegate();
        
        // 创建订单上下文
        ExpressOrderContext context = new ExpressOrderContext();
        context.setOrderModel(orderModel);
        context.setChangedPropertyDelegate(changedPropertyDelegate);
        
        // 执行修改校验，不应该因为电商退货限制抛出异常
        try {
            modifyWhiteExtension.execute(context);
            // 如果没有抛出异常，测试通过
        } catch (BusinessDomainException e) {
            // 如果抛出的异常不是电商退货地址修改限制相关的，则忽略
            if (e.getMessage().contains("电商退货产品揽收前不允许修改省市区地址")) {
                fail("非电商退货产品不应该受到电商退货地址修改限制");
            }
            // 其他业务异常，测试通过
        }
    }

    /**
     * 测试B2C业务不受电商退货地址修改限制
     */
    @Test
    public void testB2CBusinessNotRestricted() {
        // 创建电商退货订单（B2C业务，揽收前状态）
        ExpressOrderModel orderModel = createEcommerceReturnOrder(OrderStatusEnum.WAIT_ACCEPT);
        orderModel.setBusinessIdentity("B2C"); // 设置为B2C业务
        
        // 创建地址变更
        ChangedPropertyDelegate changedPropertyDelegate = createProvinceChangedDelegate();
        
        // 创建订单上下文
        ExpressOrderContext context = new ExpressOrderContext();
        context.setOrderModel(orderModel);
        context.setChangedPropertyDelegate(changedPropertyDelegate);
        
        // 执行修改校验，不应该因为电商退货限制抛出异常
        try {
            modifyWhiteExtension.execute(context);
            // 如果没有抛出异常，测试通过
        } catch (BusinessDomainException e) {
            // 如果抛出的异常不是电商退货地址修改限制相关的，则忽略
            if (e.getMessage().contains("电商退货产品揽收前不允许修改省市区地址")) {
                fail("B2C业务不应该受到电商退货地址修改限制");
            }
            // 其他业务异常，测试通过
        }
    }

    /**
     * 创建电商退货订单模型
     */
    private ExpressOrderModel createEcommerceReturnOrder(OrderStatusEnum orderStatus) {
        ExpressOrderModel orderModel = new ExpressOrderModel();
        orderModel.setOrderNo("EO0020040892960");
        orderModel.setMajorProductNo(ProductEnum.ECOMMERCE_RETURN.getCode());
        orderModel.setBusinessIdentity("C2C");
        
        // 创建订单快照
        OrderSnapshot snapshot = new OrderSnapshot();
        snapshot.setOrderStatus(orderStatus);
        orderModel.setOrderSnapshot(snapshot);
        
        return orderModel;
    }

    /**
     * 创建非电商退货订单模型
     */
    private ExpressOrderModel createNonEcommerceReturnOrder(OrderStatusEnum orderStatus) {
        ExpressOrderModel orderModel = new ExpressOrderModel();
        orderModel.setOrderNo("EO0020040892961");
        orderModel.setMajorProductNo(ProductEnum.THS.getCode()); // 特惠送产品
        orderModel.setBusinessIdentity("C2C");
        
        // 创建订单快照
        OrderSnapshot snapshot = new OrderSnapshot();
        snapshot.setOrderStatus(orderStatus);
        orderModel.setOrderSnapshot(snapshot);
        
        return orderModel;
    }

    /**
     * 创建省份地址变更委托
     */
    private ChangedPropertyDelegate createProvinceChangedDelegate() {
        ChangedPropertyDelegate delegate = new ChangedPropertyDelegate();
        // 模拟省份地址变更
        delegate.addChangedProperty(ModifyItemConfigEnum.CONSIGNEE_PROVINCE_NO_GIS, "110000", "310000");
        return delegate;
    }

    /**
     * 创建城市地址变更委托
     */
    private ChangedPropertyDelegate createCityChangedDelegate() {
        ChangedPropertyDelegate delegate = new ChangedPropertyDelegate();
        // 模拟城市地址变更
        delegate.addChangedProperty(ModifyItemConfigEnum.CONSIGNEE_CITY_NO_GIS, "110100", "310100");
        return delegate;
    }

    /**
     * 创建县区地址变更委托
     */
    private ChangedPropertyDelegate createCountyChangedDelegate() {
        ChangedPropertyDelegate delegate = new ChangedPropertyDelegate();
        // 模拟县区地址变更
        delegate.addChangedProperty(ModifyItemConfigEnum.CONSIGNEE_COUNTY_NO_GIS, "110101", "310101");
        return delegate;
    }
}
