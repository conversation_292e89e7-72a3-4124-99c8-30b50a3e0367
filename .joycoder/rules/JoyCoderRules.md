# JoyCoder Rules for jdl-oms-express Project

## 1. 项目结构和兼容性

1.1 使用Java 1.6版本进行编译，以确保广泛的兼容性。
1.2 使用Maven进行项目构建和依赖管理。
1.3 包结构应遵循 `cn.jdl.oms.core.model` 的命名规范。

## 2. 代码风格和质量

2.1 所有类都应实现 Serializable 接口，便于网络传输和持久化。
2.2 使用详细的Java文档注释，包括类描述、作者、创建日期、版本等信息。
2.3 使用 @version 标注版本信息。
2.4 代码应遵循Java编码规范，保持一致的缩进和命名风格。

## 3. 数据验证和安全性

3.1 使用Hibernate Validator注解（如@Length）进行数据验证。
3.2 对敏感字段（如账号、用户名）设置合理的长度限制。
3.3 避免在代码中硬编码敏感信息。
3.4 使用@Valid注解对嵌套对象进行验证。
3.5 为验证注解提供明确的错误信息，如：@Length(max = 50, message = "商品编码(goodsNo)长度不能超过50")。

## 4. 设计模式和最佳实践

4.1 使用继承结构（如BaseInfo）来减少代码重复。
4.2 对于可能需要扩展的字段，使用Map<String, String>类型的extendProps。
4.3 使用Lombok注解简化代码：
   - 使用@Getter和@Setter自动生成getter和setter方法。
   - 使用@Data生成所有常用方法（getter, setter, equals, hashCode, toString）。
   - 使用@Builder实现构建者模式。
   - 使用@Slf4j自动创建日志对象。
4.4 使用BigDecimal类型处理金额和精确数值。
4.5 在pom.xml中添加Lombok依赖并在IDE中安装Lombok插件。
4.6 使用专门的类型来表示特定的数据，如MoneyInfo表示金额，QuantityInfo表示数量。
4.7 对于可能需要扩展的字段，使用Map<String, String>类型的extendProps。
4.8 使用List<T>来表示一对多的关系，如List<SerialInfo> goodsSerialInfos。
4.9 为复杂对象提供无参构造函数。

## 5. 注释和文档

5.1 每个类都应有清晰的类级别注释，说明其用途和重要性。
5.2 对于复杂的业务逻辑或特殊的设计决策，应提供详细的方法级别注释。
5.3 使用 @see 标签引用相关的类或方法。
5.4 类级别注释应包含@description、@author、@createDate和@version信息。
5.5 对于重要的字段，提供详细的注释说明其用途和可能的值。
5.6 使用统一的注释风格，保持整个项目的一致性。

## 6. 性能考虑

6.1 注意序列化的影响，合理使用 transient 关键字。
6.2 对于大型集合或复杂对象，考虑使用懒加载策略。

## 7. 错误处理

7.1 使用自定义异常类处理业务逻辑错误。
7.2 提供有意义的错误消息，便于调试和日志记录。

## 8. 测试

8.1 为每个模型类编写单元测试。
8.2 测试应覆盖正常情况和边界情况。

## 9. 版本控制

9.1 使用语义化版本控制（Semantic Versioning）。
9.2 在pom.xml中明确指定项目版本。

## 10. 持续集成/持续部署 (CI/CD)

10.1 配置自动化构建和测试流程。
10.2 在合并到主分支之前，确保所有测试通过。

## 11. Lombok使用规范

11.1 优先使用Lombok注解来减少样板代码，提高开发效率。
11.2 使用@Slf4j注解进行日志声明，而不是手动创建日志对象。
11.3 对于不可变的类，使用@Value注解而不是@Data。
11.4 使用@Builder注解时，考虑是否需要同时使用@AllArgsConstructor。
11.5 注意Lombok注解可能带来的潜在问题，如循环依赖或不必要的方法生成。

## 12. 序列化规范

12.1 所有模型类都应实现Serializable接口。
12.2 为serialVersionUID提供一个明确的值，如：private static final long serialVersionUID = 1788057773230641964L;
12.3 考虑哪些字段不需要序列化，并使用transient关键字标记它们。

## 13. 命名规范

13.1 类名应使用驼峰命名法，并以Info结尾，如GoodsInfo, MoneyInfo等。
13.2 方法名应使用驼峰命名法，getter和setter方法应遵循Java Bean规范。
13.3 常量应全部大写，单词间用下划线分隔，如SERIAL_VERSION_UID。
13.4 包名应全部小写，并使用点号分隔，如cn.jdl.oms.core.model。

## 14. 异常处理

14.1 使用自定义异常类来表示业务逻辑错误。
14.2 在方法的Javadoc中使用@throws标签说明可能抛出的异常。
14.3 避免捕获和忽略异常，如果必须这么做，请提供详细的注释说明原因。

## 15. 类结构规范

15.1 类结构组织应遵循以下顺序：
   1. 类级别注释
   2. 包声明
   3. 导入语句
   4. 类定义
   5. 常量定义（如serialVersionUID）
   6. 属性定义（所有字段）
   7. 构造函数
   8. getter/setter方法
   9. 业务方法
   10. 私有辅助方法
   11. 内部类或接口

15.2 属性和方法的位置规范：
   - **属性定义**必须放在类的顶部，在常量定义之后
   - **方法定义**必须放在类的底部，在所有属性定义之后
   - 新增属性应添加在最后一个属性定义之后
   - 新增方法应添加在最后一个方法定义之后

15.3 类结构示例：
```java
/**
 * 类级别注释
 */
package cn.jdl.oms.core.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class ExampleInfo implements Serializable {
    // 常量定义
    private static final long serialVersionUID = 1234567890L;
    
    // 属性定义
    private String propertyOne;
    private Integer propertyTwo;
    private List<String> propertyThree;
    
    // 构造函数
    public ExampleInfo() {
        // 初始化代码
    }
    
    // getter/setter方法
    public String getPropertyOne() {
        return propertyOne;
    }
    
    public void setPropertyOne(String propertyOne) {
        this.propertyOne = propertyOne;
    }
    
    // 其他getter/setter方法...
    
    // 业务方法
    public void businessMethod() {
        // 业务逻辑
    }
    
    // 私有辅助方法
    private void helperMethod() {
        // 辅助逻辑
    }
    
    // 内部类
    private static class InnerClass {
        // 内部类实现
    }
}
```

15.4 代码风格检查：
   - 使用IDE的代码格式化功能确保代码风格一致
   - 在提交代码前进行代码风格检查
   - 使用静态代码分析工具（如Checkstyle、PMD）验证代码结构是否符合规范

## 16. 创建DUCC开关
    - 在BatrixSwitchKey中最后方增加静态字符串常量属性
    - 在switch.properties中增加开关配置

遵循这些规则将有助于保持代码的一致性、可读性和可维护性，同时确保项目的质量和可扩展性。这些规则应该定期审查和更新，以适应项目的发展和新的最佳实践。
