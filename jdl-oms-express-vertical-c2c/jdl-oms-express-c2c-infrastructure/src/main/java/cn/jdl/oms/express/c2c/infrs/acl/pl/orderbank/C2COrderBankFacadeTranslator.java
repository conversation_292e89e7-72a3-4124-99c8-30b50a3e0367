package cn.jdl.oms.express.c2c.infrs.acl.pl.orderbank;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.ecard.EcardFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.ecard.EcardFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest.ConsigneeInfo;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest.PosJfYun;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.PayModeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.ReceiveTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.ots.OrderResourceFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.MerchantUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.OTSLedgerUtil;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.OrderBankCurrencyUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ChannelUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Activity;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.OrderBankConstant;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.ECardDisableReasonEnum;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.TypeConversion;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.CharUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.jdl.oms.express.shared.common.constant.BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE;
import static cn.jdl.oms.express.shared.common.constant.OrderConstants.SELF_PICKUP_TEMPORARY_STORAGE_PREFIX;
import static cn.jdl.oms.express.shared.common.constant.OrderConstants.YES_VAL;

/**
 * @Package： cn.jdl.oms.express.c2c.infrs.acl.pl.orderbank
 * @ClassName: C2COrderBankFacadeTranslator
 * @Description:
 * @Author： zhangqi
 * @CreateDate 2021/3/31 19:22
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Translator
public class C2COrderBankFacadeTranslator {

    private static final Logger LOGGER = LoggerFactory.getLogger(C2COrderBankFacadeTranslator.class);

    /**
     * 台账防腐层转换通用工具类
     */
    @Resource
    private OrderBankFacadeTranslator orderBankFacadeTranslator;

    /**
     * POS寄付业务编码
     */
    private static final String POS_JF = "JF";

    /**
     * POS到付台账时，传此值
     */
    private static final String POS_TYPE = "2";

    /**
     * 外单台账C2C默认商户ID
     */
    private static final String OTS_CREATE_C2C_DEFAULT_MERCHANT_ID = "10051";
    /**
     * 外单台账C2C默认订单折扣
     */
    private static final BigDecimal OTS_CREATE_C2C_DEFAULT_DISCOUNT = BigDecimal.ZERO;
    /**
     * 外单台账C2C默认运费
     */
    private static final BigDecimal OTS_CREATE_C2C_DEFAULT_YUN = BigDecimal.ZERO;
    /**
     * 外单台账C2C默认币种
     */
    private static final Integer OTS_CREATE_C2C_DEFAULT_CURRENCY = 1;
    /**
     * 外单台账C2C默认订单类型
     */
    private static final Integer OTS_CREATE_C2C_DEFAULT_ORDER_TYPE = 1;

    /**
     * 外单台账C2C默认支付方式
     */
    private static final Integer OTS_CREATE_C2C_DEFAULT_PAY_MODE = 4;
    /**
     * 外单台账C2C默认版本号
     */
    private static final Integer OTS_CREATE_C2C_DEFAULT_VER = 0;

    /**
     * 外单台账明细C2C默认应收类型
     */
    private static final Integer OTS_CREATE_C2C_RECEIVABLE_TYPE = 1;

    /**
     * B商家新增默认 1 青龙
     */
    private static final Integer B_MERCHANT_CREATE_DEFAULT_DATA_SOURCES = 1;

    /**
     * B商家新增默认 5 港澳青龙
     */
    private static final Integer B_MERCHANT_CREATE_DATA_SOURCES_LDOP_HM = 5;

    /**
     * B商家新增默认 6 税金
     */
    private static final Integer B_MERCHANT_CREATE_DATA_SOURCE_TAX = 6;

    /**
     * B商家新增默认 7 改址
     */
    private static final Integer B_MERCHANT_CREATE_DATA_SOURCE_READDRESS = 7;

    /**
     * B商家新增默认 4 询价服务-自提暂存
     */
    private static final Integer B_MERCHANT_CREATE_DATA_SOURCE_SERVICE_ENQUIRY = 4;

    /**
     * // TODO 250618 后删除
     * 写pos台账，打标判断折扣编码集合
     * 若折扣类型存在2-揽派同时、3-专业市场、4-渠道优惠、10-散单批量寄、11-员工福利寄
     */
    private static final HashSet<String> disCountSet = new HashSet() {
        {
            addAll(Arrays.asList("2", "3", "4","10","11"));
        }
    };
    /**
     * 写pos台账，打标判断折扣编码集合（新）
     * 若折扣类型存在2-揽派同时、3-专业市场、4-渠道优惠、5-客户现结、6-合同客户、10-散单批量寄、11-员工福利寄
     */
    private static final HashSet<String> DISCOUNT_SET = new HashSet<String>() {
        {
            addAll(Arrays.asList("2", "3", "4", "6","10","11"));
        }
    };

    @Resource
    private EcardFacade ecardFacade;

    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /** 开关 */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 台账防腐请求数据对象
     *
     * @param expressOrderContext
     * @return
     */
    public OrderBankFacadeRequest toCreateOrderBankFacadeRequest(ExpressOrderContext expressOrderContext, RequestProfile requestProfile) {
        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        OrderBankFacadeRequest orderBankFacadeRequest = toCommonOrderBankFacadeRequest(orderModel, requestProfile.getTenantId());
        // B 商家创建下面分支中都有用到，所以提到最外层
        OrderBankFacadeRequest.BMerchantCreate bMerchantCreate = new OrderBankFacadeRequest.BMerchantCreate();
        List<OrderBankFacadeRequest.BMerchantDueDetailInfo> dueDetailInfos = new ArrayList<>();
        bMerchantCreate.setBMerchantDueDetailInfos(dueDetailInfos);
        // B商家数据来源
        if(orderModel.isHKMO()) {
            bMerchantCreate.setDataSources(B_MERCHANT_CREATE_DATA_SOURCES_LDOP_HM);
        } else {
            bMerchantCreate.setDataSources(B_MERCHANT_CREATE_DEFAULT_DATA_SOURCES);
        }
        // B商家 商家信息
        if (expressOrderContext.getCustomerConfig() != null) {
            bMerchantCreate.setSellerId(String.valueOf(expressOrderContext.getCustomerConfig().getCustomerId()));
            bMerchantCreate.setSellerName(expressOrderContext.getCustomerConfig().getCustomerName());
        }
        // 根据 **代收货款** 判读支付模式
        if (Objects.equals(GetFieldUtils.getCODMoney(expressOrderContext), BigDecimal.ZERO)) { //有代收货款增值服务则赋值1
            bMerchantCreate.setPayMode(PayModeEnum.ONLINE);
        } else {
            bMerchantCreate.setPayMode(PayModeEnum.COD);
        }
        orderBankFacadeRequest.setBMerchantCreate(bMerchantCreate);

        // 寄付月结：结算方式为月结的，若有代收货款则取代收货款赋值到POS到付台账和B商家台账的应收金额
        if (SettlementTypeEnum.MONTHLY_PAYMENT.equals(settlementType)) {
            BigDecimal codMoney = GetFieldUtils.getCODMoney(expressOrderContext);
            // pos到付对象赋值 cod
            if (codMoney.compareTo(BigDecimal.ZERO) > 0) {
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(codMoney);
                posYun.setPosType(POS_TYPE);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, null, orderModel.getFinance(), settlementType, true)));
                orderBankFacadeRequest.setPosYun(posYun);
            }

            //pos寄付直接写0
            /*OrderBankFacadeRequest.PosJfYun posJfYun = generateZeroPosJfYun(orderModel, orderModel.getFinance().getSettlementType());
            orderBankFacadeRequest.setPosJfYun(posJfYun);*/

            // B商家详情-货款赋值 cod
            OrderBankFacadeRequest.BMerchantDueDetailInfo dueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            dueDetailInfo.setAmount(codMoney);
            dueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
            dueDetailInfos.add(dueDetailInfo);

            // B商家详情-到付赋值 0
            OrderBankFacadeRequest.BMerchantDueDetailInfo dfDueDetailInfo = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
            dueDetailInfos.add(dfDueDetailInfo);

            // B商家详情-寄付赋值 0
            OrderBankFacadeRequest.BMerchantDueDetailInfo jfDueDetailInfo = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
            dueDetailInfos.add(jfDueDetailInfo);

            // B商家总额赋值 cod
            bMerchantCreate.setAmount(codMoney);
        } else if (SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)) { // 非月结的POS寄付：取订单财务的折后金额
            BigDecimal amount = BigDecimal.ZERO;
            BigDecimal discountMoney = GetFieldUtils.getDiscountMoney(orderModel);
            // pos寄付赋值 折后金额
            /*OrderBankFacadeRequest.PosJfYun posJfYun = new OrderBankFacadeRequest.PosJfYun();
            posJfYun.setAmount(discountMoney);
            posJfYun.setBusinessNo(POS_JF);
            posJfYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, settlementType, false)));
            orderBankFacadeRequest.setPosJfYun(posJfYun);*/
            BigDecimal codMoney = GetFieldUtils.getCODMoney(expressOrderContext);
            // pos到付赋值 cod
            if (codMoney.compareTo(BigDecimal.ZERO) > 0) {
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(codMoney);
                posYun.setPosType(POS_TYPE);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, null, orderModel.getFinance(), settlementType, true)));
                orderBankFacadeRequest.setPosYun(posYun);
            }

            // B商家详情-寄付赋值 折后金额
            OrderBankFacadeRequest.BMerchantDueDetailInfo jfDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            jfDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
            jfDueDetailInfo.setAmount(discountMoney);
            dueDetailInfos.add(jfDueDetailInfo);
            amount = amount.add(discountMoney);

            // B商家详情-货款赋值 cod
            OrderBankFacadeRequest.BMerchantDueDetailInfo codDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            codDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
            codDueDetailInfo.setAmount(codMoney);
            dueDetailInfos.add(codDueDetailInfo);
            amount = amount.add(codMoney);

            // B商家详情到付赋值 0
            OrderBankFacadeRequest.BMerchantDueDetailInfo dfDueDetailInfo = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
            bMerchantCreate.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderModel));
            dueDetailInfos.add(dfDueDetailInfo);

            // B商家总额赋值 cod + 折后金额
            bMerchantCreate.setAmount(amount);
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)) { // 非月结的POS到付：取订单财务的折后金额+代收货款(若有)
            BigDecimal amount = BigDecimal.ZERO;
            BigDecimal codMoney = GetFieldUtils.getCODMoney(expressOrderContext);
            // B商家详情-货款赋值 cod
            OrderBankFacadeRequest.BMerchantDueDetailInfo codDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            codDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
            codDueDetailInfo.setAmount(codMoney);
            dueDetailInfos.add(codDueDetailInfo);
            amount = amount.add(codMoney);

            BigDecimal discountMoney = GetFieldUtils.getDiscountMoney(orderModel);

            /*
             * 淘天逆向建新单时（渠道.特殊来源=淘天），新单结算方式=到付现结、支付环节=后款。
             * 淘天来源，初始化台账，逆向新单B商家和POS台账 折后金额写0
             */
            if (ChannelUtil.isTaoTianSpecial(orderModel.getOrderSnapshot())
                    && discountMoney.compareTo(BigDecimal.ZERO) > 0) {

                LOGGER.info("淘天来源，初始化台账，逆向新单B商家和POS台账 折后金额置0。discountMoney: {} -> 0。", discountMoney);
                // 初始化时写0
                discountMoney = BigDecimal.ZERO;

            }

            // B商家详情-寄付赋值 折后金额
            OrderBankFacadeRequest.BMerchantDueDetailInfo dfDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            dfDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
            dfDueDetailInfo.setAmount(discountMoney);
            bMerchantCreate.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderModel));
            dueDetailInfos.add(dfDueDetailInfo);
            amount = amount.add(discountMoney);

            // pos到付赋值 cod + 折后金额
            if (amount.compareTo(BigDecimal.ZERO) > 0
                    // 淘天来源，初始化台账，逆向新单B商家和POS台账 允许写0
                    || (ChannelUtil.isTaoTianSpecial(orderModel.getOrderSnapshot()) && amount.compareTo(BigDecimal.ZERO) >= 0)) {
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(amount);
                posYun.setPosType(POS_TYPE);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, null, orderModel.getFinance(), settlementType, true)));
                orderBankFacadeRequest.setPosYun(posYun);
            }
            //pos寄付直接写0
            /*OrderBankFacadeRequest.PosJfYun posJfYun = generateZeroPosJfYun(orderModel, orderModel.getFinance().getSettlementType());
            orderBankFacadeRequest.setPosJfYun(posJfYun);*/

            // B商家详情-寄付赋值 0
            OrderBankFacadeRequest.BMerchantDueDetailInfo jfDueDetailInfo = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
            dueDetailInfos.add(jfDueDetailInfo);
            // B商家总额赋值 cod + 折后金额
            bMerchantCreate.setAmount(amount);
        }
        //处理币种
        OrderBankCurrencyUtil.handleCurrency(expressOrderContext, orderBankFacadeRequest);
        // 处理国际出口的省信息
        OTSLedgerUtil.setOrderBankProvinceForIntlExport(orderModel, orderBankFacadeRequest);
        return orderBankFacadeRequest;
    }

    /**
     * 生成WaybillSign
     * 1.结算方式为到付现结则25位赋值2
     * 2.结算方式为寄付现结则25位赋值3
     * 3.交易业务单元为C2C和O2O则29位赋值8
     * 4.交易业务单元为C2C和O2O则其他位都赋值0
     *
     * @param expressOrderModel
     * @param settlementType
     * @return
     */
    public char[] getWaybillSign(ExpressOrderModel expressOrderModel, ExpressOrderModel orderSnapshot,Finance finance, SettlementTypeEnum settlementType, boolean dfFee) {
        char[] waybillSign = String.format("%0200d", 0).toCharArray();
        if (settlementType == SettlementTypeEnum.CASH_ON_DELIVERY) {
            waybillSign[24] = '2';
        } else if (settlementType == SettlementTypeEnum.CASH_ON_PICK) {
            waybillSign[24] = '3';
        }
        if (expressOrderModel.isC2C() || expressOrderModel.isO2O()) {
            waybillSign[28] = '8';
        }

        ECardDisableReasonEnum reasonEnum = orderBankFacadeTranslator.isDisableECard(expressOrderModel, orderSnapshot, finance, dfFee);
        if (reasonEnum == null) {
            waybillSign[0] = CharUtils.toChar(YES_VAL);
        }

        return waybillSign;
    }

    private char[] getWaybillSignForModify(ExpressOrderModel expressOrderModel, ExpressOrderModel refOrder, SettlementTypeEnum settlementType, boolean dfFee) {
        char[] waybillSign = String.format("%0200d", 0).toCharArray();
        if (settlementType == SettlementTypeEnum.CASH_ON_DELIVERY) {
            waybillSign[24] = '2';
        } else if (settlementType == SettlementTypeEnum.CASH_ON_PICK) {
            waybillSign[24] = '3';
        }
        if (expressOrderModel.isC2C() || expressOrderModel.isO2O()) {
            waybillSign[28] = '8';
        }

        waybillSign[0] = waybillSignZeroBitForModify(expressOrderModel, refOrder, dfFee);

        return waybillSign;
    }

    private char waybillSignZeroBitForModify(ExpressOrderModel orderModel, ExpressOrderModel refOrder, boolean dfFee) {
        LOGGER.debug("waybillSignZeroBit,orderModel:{},settlementType:{}", JSONUtils.beanToJSONDefault(orderModel), dfFee);

        if(orderModel.isHKMO()){
            LOGGER.info("港澳订单不支持 E卡");
            return '0';
        }

        //判断修改场景
        LOGGER.debug("waybillSignZeroBit,getPromotion:{}", JSONUtils.beanToJSONDefault(orderModel.getPromotion()));
        //优惠券与活动传入全量的，这个直接使用新单
        if (orderModel.getPromotion() != null && !CollectionUtils.isEmpty(orderModel.getPromotion().getTickets())) {
            LOGGER.info("waybillSignZeroBit,不支持E卡，优惠券 ");
            return '0';
        }
        if (orderModel.getPromotion() != null && !CollectionUtils.isEmpty(orderModel.getPromotion().getActivities())) {
            for (Activity activity : orderModel.getPromotion().getActivities()) {
                if ("GRADUATION_SEND".equals(activity.getActivityNo())) {
                    LOGGER.info("waybillSignZeroBit,不支持E卡，活动 ");
                    return '0';
                }
            }
        }

        //POS到付应收里同时写了到付运费和代收货款
        //到付现结带cod，且不为0
        Product product = orderModel.getProductDelegate() != null ? orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.JDL_COD_TOC.getCode()) : null;
        if (product != null) {
            BigDecimal cod = TypeConversion.stringToBigDecimal(product.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()), DEFAULT_AMOUNT_DECIMAL_SCALE, null);
            if (cod != null && cod.compareTo(BigDecimal.ZERO) > 0) {
                LOGGER.info("waybillSignZeroBit,不支持E卡，到付cod ");
                return '0';
            }
        }

        // 判断是否为偏远流向
        String fromProvinceCode = Optional.ofNullable(GetFieldUtils.getField(orderModel, GetFieldUtils.CONSIGNOR_ADDRESS))
                .map(Address::getProvinceNoGis)
                .orElse(null);
        String toProvinceCode = Optional.ofNullable(GetFieldUtils.getField(orderModel, GetFieldUtils.CONSIGNEE_ADDRESS))
                .map(Address::getProvinceNoGis)
                .orElse(null);
        if (orderBankFacadeTranslator.isRemoteRoute(fromProvinceCode, toProvinceCode)) {
            LOGGER.info("偏远流向不支持E卡支付");
            return '0';
        }

        if (orderModel.getFinance() != null && !CollectionUtils.isEmpty(orderModel.getFinance().getFinanceDetails())) {
            boolean eCardSwitch = BatrixSwitch.applyByBoolean(BatrixSwitchKey.E_CARD_DISCOUNT_SWITCH);
            for (FinanceDetail financeDetail : orderModel.getFinance().getFinanceDetails()) {
                if (!CollectionUtils.isEmpty(financeDetail.getDiscounts())) {
                    for (Discount discount : financeDetail.getDiscounts()) {
                        if (eCardSwitch) {
                            if (DISCOUNT_SET.contains(discount.getDiscountType())) {
                                LOGGER.info("waybillSignZeroBit,不支持E卡, {}", discount.getDiscountType());
                                return '0';
                            }
                        } else {
                            if (disCountSet.contains(discount.getDiscountType())) {
                                LOGGER.info("waybillSignZeroBit,不支持E卡，新单2，3，10折扣码 ");
                                return '0';
                            }
                        }
                    }
                }
            }
        } else if (refOrder.getFinance() != null && !CollectionUtils.isEmpty(refOrder.getFinance().getFinanceDetails())) {
            boolean eCardSwitch = BatrixSwitch.applyByBoolean(BatrixSwitchKey.E_CARD_DISCOUNT_SWITCH);
            for (FinanceDetail financeDetail : refOrder.getFinance().getFinanceDetails()) {
                if (!CollectionUtils.isEmpty(financeDetail.getDiscounts())) {
                    for (Discount discount : financeDetail.getDiscounts()) {
                        if (eCardSwitch) {
                            if (DISCOUNT_SET.contains(discount.getDiscountType())) {
                                LOGGER.info("waybillSignZeroBit,不支持E卡, {}", discount.getDiscountType());
                                return '0';
                            }
                        } else {
                            if (disCountSet.contains(discount.getDiscountType())) {
                                LOGGER.info("waybillSignZeroBit,不支持E卡，原单2，3，10折扣码 ");
                                return '0';
                            }
                        }
                    }
                }
            }
        }

        if (dfFee) {
            //到付现结用派送站点ID获取派送站点子类型为46-便民站点
            if (orderModel.getShipment() != null && Integer.valueOf(46).equals(orderModel.getShipment().getEndStationType())) {
                LOGGER.info("waybillSignZeroBit,不支持E卡，新单 到付现结用派送站点ID获取派送站点子类型为46-便民站点");
                return '0';
            }
            if ((orderModel.getShipment() == null || orderModel.getShipment().getEndStationType() == null)
                    && (refOrder.getShipment() != null && Integer.valueOf(46).equals(refOrder.getShipment().getEndStationType()))) {
                LOGGER.info("waybillSignZeroBit,不支持E卡，原单 到付现结用派送站点ID获取派送站点子类型为46-便民站点");
                return '0';
            }
        } else {
            //寄付现结用揽收站点ID获取揽收站点子类型为46-便民站点
            if (orderModel.getShipment() != null && Integer.valueOf(46).equals(orderModel.getShipment().getStartStationType())) {
                LOGGER.info("waybillSignZeroBit,不支持E卡，新单 寄付现结用揽收站点ID获取揽收站点子类型为46-便民站点");
                return '0';
            }
            if ((orderModel.getShipment() == null || orderModel.getShipment().getStartStationType() == null)
                    && (refOrder.getShipment() != null && Integer.valueOf(46).equals(refOrder.getShipment().getStartStationType()))) {
                LOGGER.info("waybillSignZeroBit,不支持E卡，原单 寄付现结用揽收站点ID获取揽收站点子类型为46-便民站点");
                return '0';
            }
        }

        EcardFacadeRequest ecardFacadeRequest = null;
        if (dfFee) {
            ecardFacadeRequest = new EcardFacadeRequest();
            //到付现结用收件地址京标省市id
            ecardFacadeRequest.setProvinceId(orderModel.getConsignee().getAddress() != null && orderModel.getConsignee().getAddress().getProvinceNoGis() != null ?
                    orderModel.getConsignee().getAddress().getProvinceNoGis() : refOrder.getConsignee().getAddress().getProvinceNoGis());
            ecardFacadeRequest.setCityId(orderModel.getConsignee().getAddress() != null && orderModel.getConsignee().getAddress().getCityNoGis() != null ?
                    orderModel.getConsignee().getAddress().getCityNoGis() : refOrder.getConsignee().getAddress().getCityNoGis());
        } else {
            ecardFacadeRequest = new EcardFacadeRequest();
            //寄付现结用寄件地址京标省市id
            ecardFacadeRequest.setProvinceId(orderModel.getConsignor().getAddress() != null && orderModel.getConsignor().getAddress().getProvinceNoGis() != null ?
                    orderModel.getConsignor().getAddress().getProvinceNoGis() : refOrder.getConsignor().getAddress().getProvinceNoGis());
            ecardFacadeRequest.setCityId(orderModel.getConsignor().getAddress() != null && orderModel.getConsignor().getAddress().getCityNoGis() != null ?
                    orderModel.getConsignor().getAddress().getCityNoGis() : refOrder.getConsignor().getAddress().getCityNoGis());
        }
        if (ecardFacadeRequest != null) {
            //E卡支付查询接口，如果失败，则跳过这个判断，不影响主流程
            try {
                boolean match = ecardFacade.ecardMatch(ecardFacadeRequest);
                if (!match) {
                    LOGGER.info("该城市不支持 E卡");
                    return '0';
                }
            } catch (Exception e) {
                LOGGER.error("E卡查询错误，跳过该判断，继续主流程执行");
            }
        }

        return '1';
    }

    private OrderBankFacadeRequest initCommonModifyOrderBankFacadeRequest(ExpressOrderContext context) {
        OrderBankFacadeRequest orderBankFacadeRequest = new OrderBankFacadeRequest();

        orderBankFacadeRequest.setWaybillNo(context.getOrderModel().getRefOrderInfoDelegate().getWaybillNo());
        orderBankFacadeRequest.setUUid(context.getOrderModel().requestProfile().getTenantId() + "_" + context.getOrderModel().orderNo());

        orderBankFacadeRequest.setOrgId(context.getOrderModel().getFinance().getCollectionOrgNo());
        orderBankFacadeRequest.setOrgName(context.getOrderModel().getFinance().getCollectionOrgName());

        orderBankFacadeRequest.setConsigneeInfo(toConsigneeInfo(context.getOrderModel()));
        orderBankFacadeRequest.setConsignorInfo(toConsignorInfo(context.getOrderModel()));
        return orderBankFacadeRequest;
    }

    /**
     * 改址一单到底修改台账
     * @param context
     * @return
     */
    public OrderBankFacadeRequest toReaddressOrderBankModifyRequest(ExpressOrderContext context) {
        ExpressOrderModel orderModel = context.getOrderModel();
        OrderBankFacadeRequest orderBankFacadeRequest = toCommonOrderBankFacadeRequest(orderModel,orderModel.requestProfile().getTenantId());
        // 结算方式
        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(context);
        BigDecimal codMoney = GetFieldUtils.getCurrentCod(orderModel);
        if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)) {
            // POS到付现结：取订单财务的折后金额+代收货款(若有)
            BigDecimal discountAmount = GetFieldUtils.getDiscountMoney(orderModel);
            BigDecimal amount = getTotalMoney(codMoney, discountAmount);
            if (amount != null) {
                // pos到付
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(amount);
                posYun.setPosType(OrderBankConstant.EXPRESS_POS_TYPE);
                //pos到付wayBillSign
                //todo C2C需要校验
                posYun.setWayBillSign(String.valueOf(getWaybillSignForModify(orderModel, orderModel, settlementType, true)));
                orderBankFacadeRequest.setPosYun(posYun);
            }

            if (discountAmount != null) {
                //B商家  到付
                OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = new OrderBankFacadeRequest.BMerchantModify();
                OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                bMerchantDueDetailInfo.setAmount(discountAmount);
                bMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
                bMerchantDfModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfo);
                bMerchantDfModify.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderModel));
                orderBankFacadeRequest.setBMerchantDfModify(bMerchantDfModify);
            }
        }  else if (SettlementTypeEnum.MONTHLY_PAYMENT.equals(settlementType)) {
            // pos到付 代收货款(若有) 无代收货款则置为0，codMoney=0
            OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
            posYun.setAmount(codMoney);
            posYun.setPosType(OrderBankConstant.EXPRESS_POS_TYPE);
            posYun.setServiceCode(OrderBankConstant.B2C_SERVICE_CODE);
            posYun.setWayBillSign(String.valueOf(getWaybillSignForModify(orderModel, orderModel, settlementType, true)));
            orderBankFacadeRequest.setPosYun(posYun);

            //B商家  到付运费置为0
            OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = new OrderBankFacadeRequest.BMerchantModify();
            OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            bMerchantDueDetailInfo.setAmount(BigDecimal.ZERO);
            bMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
            bMerchantDfModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfo);
            bMerchantDfModify.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderModel));
            orderBankFacadeRequest.setBMerchantDfModify(bMerchantDfModify);
        }

        // B商家cod
        OrderBankFacadeRequest.BMerchantModify bMerchantCodModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
        bMerchantDueDetailInfo.setAmount(codMoney);
        bMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
        bMerchantCodModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfo);
        orderBankFacadeRequest.setBMerchantCodModify(bMerchantCodModify);
        //处理币种
        OrderBankCurrencyUtil.handleCurrency(orderModel, orderBankFacadeRequest);
        return orderBankFacadeRequest;
    }

    /**
     * 改址一单到底修改台账
     * @param orderBankAmount 写账金额
     * @return
     */
    public OrderBankFacadeRequest toReaddressOrderBankModifyRequest(ExpressOrderModel orderModel, String modifyRecordNo, SettlementTypeEnum settlementType, BigDecimal orderBankAmount, boolean isWayBillNo) {
        OrderBankFacadeRequest orderBankFacadeRequest = toCommonOrderBankFacadeRequest(orderModel,orderModel.getOrderSnapshot() , orderModel.requestProfile().getTenantId());
        orderBankFacadeRequest.setWaybillNo(modifyRecordNo);
        orderBankFacadeRequest.setUUid(orderModel.requestProfile().getTenantId() + "_" + modifyRecordNo);

        if (orderBankAmount != null) {
            BigDecimal codMoney = BigDecimal.ZERO;
            if(isWayBillNo){
                codMoney = GetFieldUtils.getCod(orderModel);
            }
            // pos到付
            OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
            posYun.setAmount(orderBankAmount.add(codMoney));
            posYun.setPosType(OrderBankConstant.EXPRESS_POS_TYPE);
            //pos到付wayBillSign
            //todo C2C需要校验
            posYun.setWayBillSign(String.valueOf(getWaybillSignForModify(orderModel, Optional.ofNullable(orderModel.getOrderSnapshot()).orElse(orderModel), settlementType, true)));
            orderBankFacadeRequest.setPosYun(posYun);

            //B商家  到付
            OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = new OrderBankFacadeRequest.BMerchantModify();
            OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            bMerchantDueDetailInfo.setAmount(orderBankAmount);
            bMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
            bMerchantDfModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfo);
            orderBankFacadeRequest.setBMerchantDfModify(bMerchantDfModify);
        }
        // fixme 如果港澳改址后期涉及退款，需要处理币种。港澳同城改址改派不涉及退款
        return orderBankFacadeRequest;
    }

    /**
     * 改址一单到底修改台账
     * @param orderBankAmount 写账金额
     * @return
     */
    public OrderBankFacadeRequest toReaddressOrderBankCreateRequest(ExpressOrderModel orderModel, ModifyRecord modifyRecord, SettlementTypeEnum settlementType, BigDecimal orderBankAmount) {
        // 入参初始化
        OrderBankFacadeRequest orderBankFacadeRequest = toCommonOrderBankFacadeRequest(orderModel, orderModel.getOrderSnapshot(), orderModel.requestProfile().getTenantId());
        orderBankFacadeRequest.setWaybillNo(modifyRecord.getModifyRecordNo());
        orderBankFacadeRequest.setUUid(orderModel.requestProfile().getTenantId() + "_" + modifyRecord.getModifyRecordNo());
        OrderBankFacadeRequest.BMerchantCreate bMerchantCreate = new OrderBankFacadeRequest.BMerchantCreate();
        // 数据源
        bMerchantCreate.setDataSources(B_MERCHANT_CREATE_DATA_SOURCE_READDRESS);
        bMerchantCreate.setAmount(orderBankAmount);
        // 商家 & 支付信息
        if (orderModel.getCustomer() != null) {
            bMerchantCreate.setSellerId(String.valueOf(orderModel.getCustomer().getAccountId()));
            bMerchantCreate.setSellerName(orderModel.getCustomer().getAccountName());
        }
        if(null != orderModel.getOrderSnapshot() && null != orderModel.getOrderSnapshot().getCustomer()){
            if(StringUtils.isBlank(bMerchantCreate.getSellerId())){
                bMerchantCreate.setSellerId(String.valueOf(orderModel.getOrderSnapshot().getCustomer().getAccountId()));
            }
            if(StringUtils.isBlank(bMerchantCreate.getSellerName())){
                bMerchantCreate.setSellerId(String.valueOf(orderModel.getOrderSnapshot().getCustomer().getAccountName()));
            }
        }

        bMerchantCreate.setPayMode(PayModeEnum.ONLINE);

        if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)) {
            if (orderBankAmount != null) {
                // pos到付
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(orderBankAmount);
                posYun.setPosType(OrderBankConstant.EXPRESS_POS_TYPE);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSignForModify(orderModel, null != orderModel.getOrderSnapshot() ? orderModel.getOrderSnapshot() : orderModel, settlementType, true)));
                orderBankFacadeRequest.setPosYun(posYun);

                //B商家  到付
                List<OrderBankFacadeRequest.BMerchantDueDetailInfo> bMerchantDueDetailInfos = new ArrayList<>();
                OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                bMerchantDueDetailInfo.setAmount(orderBankAmount);
                bMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
                bMerchantDueDetailInfos.add(bMerchantDueDetailInfo);
                bMerchantCreate.setBMerchantDueDetailInfos(bMerchantDueDetailInfos);
                MerchantEnum merchant = MerchantUtils.getMerchantIdEnum(orderModel, modifyRecord);
                if (null != merchant) {
                    bMerchantCreate.setOtsMerchantId(merchant.getMerchantId());
                }
                orderBankFacadeRequest.setBMerchantCreate(bMerchantCreate);
            }
        }
        // 处理币种：港澳同城改址，异步询价（港澳没有复重复量方，同步询价）
        if (orderModel.isHKMO()) {
            OrderBankCurrencyUtil.handleCurrency(orderModel, orderBankFacadeRequest);
        }
        return orderBankFacadeRequest;
    }

    public OrderBankFacadeRequest toModifyOrderBankFacadeRequest(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();

        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);
        OrderBankFacadeRequest orderBankFacadeRequest = new OrderBankFacadeRequest();

        orderBankFacadeRequest.setWaybillNo(expressOrderContext.getOrderModel().getOrderSnapshot().getRefOrderInfoDelegate().getWaybillNo());
        orderBankFacadeRequest.setOrgId(expressOrderContext.getOrderModel().getFinance().getCollectionOrgNo());
        orderBankFacadeRequest.setOrgName(expressOrderContext.getOrderModel().getFinance().getCollectionOrgName());
        orderBankFacadeRequest.setUUid(expressOrderContext.getOrderModel().requestProfile().getTenantId() + "_" + expressOrderContext.getOrderModel().orderNo());


        orderBankFacadeRequest.setConsigneeInfo(toConsigneeInfo(expressOrderContext.getOrderModel()));
        orderBankFacadeRequest.setConsignorInfo(toConsignorInfo(expressOrderContext.getOrderModel()));
        BigDecimal discountMoney = GetFieldUtils.getDiscountMoney(expressOrderContext.getOrderModel());

        if (snapshot.getModifyRecordDelegate().isNotEmpty()) {
            //存改址记录的，折后金额应该取第1条记录的pendingMoney，若pendingMoney为空，则使用discountAmount兜底
            ModifyRecord originModifyRecord = snapshot.getModifyRecordDelegate().getOriginModifyRecord();
            FinanceInfo originFinance = ((ReaddressRecordDetailInfo) originModifyRecord.getModifyRecordDetail()).getFinance();
            MoneyInfo pendingMoney = originFinance.getPendingMoney();
            if (null != pendingMoney && null != pendingMoney.getAmount()) {
                discountMoney = pendingMoney.getAmount();
            } else if (null != originFinance.getDiscountAmount()) {
                discountMoney = originFinance.getDiscountAmount().getAmount();
            }
        }
        if (null == discountMoney) {
            discountMoney = BigDecimal.ZERO;
        }

        // 寄付月结：结算方式为月结的，若有代收货款则取代收货款赋值到POS到付台账和B商家台账的应收金额
        if (SettlementTypeEnum.MONTHLY_PAYMENT.equals(settlementType)) {
            BigDecimal amount = GetFieldUtils.getCODMoney(expressOrderContext);
            if (amount != null) {
                // pos到付
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(amount);
                posYun.setPosType(POS_TYPE);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSignForModify(expressOrderContext.getOrderModel(), expressOrderContext.getOrderModel().getOrderSnapshot(), settlementType, true)));
                orderBankFacadeRequest.setPosYun(posYun);
            }
        } else if (SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)) { // 非月结的POS寄付：取订单财务的折后金额

            if (discountMoney != null) {
                // pos寄付
                OrderBankFacadeRequest.PosJfYun posJfYun = new OrderBankFacadeRequest.PosJfYun();
                posJfYun.setAmount(discountMoney);
                posJfYun.setBusinessNo(POS_JF);
                posJfYun.setWayBillSign(String.valueOf(getWaybillSignForModify(expressOrderContext.getOrderModel(), expressOrderContext.getOrderModel().getOrderSnapshot(), settlementType, false)));
                orderBankFacadeRequest.setPosJfYun(posJfYun);
            }
            BigDecimal amount = GetFieldUtils.getCODMoney(expressOrderContext);
            if (amount != null) {
                // pos到付
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(amount);
                posYun.setPosType(POS_TYPE);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSignForModify(expressOrderContext.getOrderModel(), expressOrderContext.getOrderModel().getOrderSnapshot(), settlementType, true)));
                orderBankFacadeRequest.setPosYun(posYun);
            }
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)) { // 非月结的POS到付：取订单财务的折后金额+代收货款(若有)
            BigDecimal amount = getTotalMoney(GetFieldUtils.getCODMoney(expressOrderContext), discountMoney);
            if (amount != null) {
                // pos到付
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(amount);
                posYun.setPosType(POS_TYPE);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSignForModify(expressOrderContext.getOrderModel(), expressOrderContext.getOrderModel().getOrderSnapshot(), settlementType, true)));
                orderBankFacadeRequest.setPosYun(posYun);
            }
        }
        Product product = expressOrderContext.getOrderModel().getProductDelegate().ofProductNo(AddOnProductEnum.JDL_COD_TOC.getCode());
        if (product != null) {
            OperateTypeEnum operateType = product.getOperateType();
            // 判断 operateType，分情况赋值
            // == null , 不变
            // == delete ，0
            // == other, 代收货款
            BigDecimal codMoney;
            if (operateType != null) {
                if (operateType == OperateTypeEnum.DELETE) {
                    codMoney = BigDecimal.ZERO;
                } else {
                    String codMoneyStr = product.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode());
                    codMoney = TypeConversion.stringToBigDecimal(codMoneyStr, DEFAULT_AMOUNT_DECIMAL_SCALE, null);
                }
                OrderBankFacadeRequest.BMerchantModify bMerchantModify = new OrderBankFacadeRequest.BMerchantModify();
                OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                bMerchantDueDetailInfo.setAmount(codMoney);
                bMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
                bMerchantModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfo);
                orderBankFacadeRequest.setBMerchantModify(bMerchantModify);
            }
        }
        //处理币种
        //OrderBankCurrencyUtil.handleCurrency(expressOrderContext, orderBankFacadeRequest);
        return orderBankFacadeRequest;
    }

    /**
     * 费用合计
     *
     * @param moneys
     * @return
     */
    private BigDecimal getTotalMoney(BigDecimal... moneys) {
        if (moneys == null) {
            return null;
        }
        BigDecimal total = BigDecimal.ZERO;
        for (BigDecimal money : moneys) {
            if (money != null) {
                total = total.add(money);
            }
        }
        return total;
    }


    /**
     * 外单台账新增
     *
     * @param orderBankFacadeRequest
     * @return
     */
    public OrderResourceFacadeRequest toOrderResourceFacadeRequest(OrderBankFacadeRequest orderBankFacadeRequest) {
        OrderResourceFacadeRequest orderResourceFacadeRequest = new OrderResourceFacadeRequest();
        orderResourceFacadeRequest.setOrderId(orderBankFacadeRequest.getWaybillNo());
        orderResourceFacadeRequest.setMerchantId(orderBankFacadeRequest.getOtsCreate().getMerchantId());
        orderResourceFacadeRequest.setTotalPrice(orderBankFacadeRequest.getOtsCreate().getTotalPrice());
        orderResourceFacadeRequest.setOrderPrice(orderBankFacadeRequest.getOtsCreate().getOrderPrice());
        orderResourceFacadeRequest.setDiscount(orderBankFacadeRequest.getOtsCreate().getDiscount());
        orderResourceFacadeRequest.setYun(orderBankFacadeRequest.getOtsCreate().getYun());
        orderResourceFacadeRequest.setPin(orderBankFacadeRequest.getOtsCreate().getPin());
        orderResourceFacadeRequest.setCurrency(orderBankFacadeRequest.getOtsCreate().getCurrency());
        orderResourceFacadeRequest.setOrderCode(Integer.valueOf(orderBankFacadeRequest.getOrgId()));
        orderResourceFacadeRequest.setOrderType(orderBankFacadeRequest.getOtsCreate().getOrderType());
        orderResourceFacadeRequest.setOrderTime(orderBankFacadeRequest.getOtsCreate().getOrderTime());
        orderResourceFacadeRequest.setPayMode(orderBankFacadeRequest.getOtsCreate().getPayMode());
        orderResourceFacadeRequest.setVer(orderBankFacadeRequest.getOtsCreate().getVer());
        orderResourceFacadeRequest.setReceivableDetails(orderBankFacadeRequest.getOtsCreate().getReceivableDetails());
        return orderResourceFacadeRequest;
    }

    /**
     * 构建清台账公共参数
     *
     * @param expressOrderContext
     * @param clearHis            清理历史台账信息
     * @return
     */
    public OrderBankFacadeRequest toClearCommonOrderBankFacadeRequest(ExpressOrderContext expressOrderContext, boolean clearHis) {
        ExpressOrderModel orderModel;
        if (clearHis) {
            orderModel = expressOrderContext.getOrderModel().getOrderSnapshot();
        } else {
            orderModel = expressOrderContext.getOrderModel();
        }
        OrderBankFacadeRequest orderBankFacadeRequest = toCommonOrderBankFacadeRequest(orderModel, expressOrderContext.getOrderModel().requestProfile().getTenantId());
        SettlementTypeEnum settlementType = orderModel.getFinance().getSettlementType();

        //pos到付
        OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
        if (orderModel.isFreight()) {
            posYun.setPosType(OrderBankConstant.FREIGHT_POS_TYPE);
        } else {
            posYun.setPosType(C2COrderBankFacadeTranslator.POS_TYPE);
        }
        posYun.setAmount(BigDecimal.ZERO);
        posYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, null, orderModel.getFinance(), settlementType, true)));
        orderBankFacadeRequest.setPosYun(posYun);


        //pos寄付
        PosJfYun posJfYun = new PosJfYun();
        posJfYun.setBusinessNo(POS_JF);
        posJfYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, null, orderModel.getFinance(), settlementType, false)));
        posJfYun.setAmount(BigDecimal.ZERO);
        orderBankFacadeRequest.setPosJfYun(posJfYun);

        return orderBankFacadeRequest;
    }

    /**
     * 构建公共参数
     *
     * @param expressOrderModel
     * @return
     */
    public OrderBankFacadeRequest toCommonOrderBankFacadeRequest(ExpressOrderModel expressOrderModel, String tenantId) {
        OrderBankFacadeRequest orderBankFacadeRequest = new OrderBankFacadeRequest();
        if (expressOrderModel.isSelfPickupTemporaryStorageOrder()) {
            // 自提暂存询价服务单，台账/收入集成/开票使用ZTZC+三方运单号
            orderBankFacadeRequest.setWaybillNo(OrderConstants.SELF_PICKUP_TEMPORARY_STORAGE_PREFIX + expressOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        } else {
            orderBankFacadeRequest.setWaybillNo(expressOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        }
        orderBankFacadeRequest.setOrgId(expressOrderModel.getFinance().getCollectionOrgNo());
        orderBankFacadeRequest.setOrgName(expressOrderModel.getFinance().getCollectionOrgName());
        orderBankFacadeRequest.setConsigneeInfo(toConsigneeInfo(expressOrderModel));
        orderBankFacadeRequest.setConsignorInfo(toConsignorInfo(expressOrderModel));
        orderBankFacadeRequest.setUUid(tenantId + "_" + expressOrderModel.orderNo());
        orderBankFacadeRequest.setAccountNo(expressOrderModel.getCustomer().getAccountNo());
        return orderBankFacadeRequest;
    }

    /**
     * 构建公共参数
     *
     * @param expressOrderModel
     * @return
     */
    public OrderBankFacadeRequest toCommonOrderBankFacadeRequest(ExpressOrderModel expressOrderModel,ExpressOrderModel snapshot, String tenantId) {
        OrderBankFacadeRequest orderBankFacadeRequest = new OrderBankFacadeRequest();
        orderBankFacadeRequest.setWaybillNo(StringUtils.isNotBlank(expressOrderModel.getRefOrderInfoDelegate().getWaybillNo()) ? expressOrderModel.getRefOrderInfoDelegate().getWaybillNo()
                : null != snapshot ? (StringUtils.isNotBlank(snapshot.getRefOrderInfoDelegate().getWaybillNo()) ? snapshot.getRefOrderInfoDelegate().getWaybillNo() : ("")) : "");

        orderBankFacadeRequest.setOrgId(StringUtils.isNotBlank(expressOrderModel.getFinance().getCollectionOrgNo()) ? expressOrderModel.getFinance().getCollectionOrgNo()
                : null != snapshot ? (StringUtils.isNotBlank(snapshot.getFinance().getCollectionOrgNo()) ? snapshot.getFinance().getCollectionOrgNo() : ("")) : "");

        orderBankFacadeRequest.setOrgName(StringUtils.isNotBlank(expressOrderModel.getFinance().getCollectionOrgName()) ? expressOrderModel.getFinance().getCollectionOrgName()
                : null != snapshot ? (StringUtils.isNotBlank(snapshot.getFinance().getCollectionOrgName()) ? snapshot.getFinance().getCollectionOrgName() : ("")) : "");
        orderBankFacadeRequest.setConsigneeInfo(toConsigneeInfo(expressOrderModel));
        orderBankFacadeRequest.setConsignorInfo(toConsignorInfo(expressOrderModel));
        orderBankFacadeRequest.setUUid(tenantId + "_" + expressOrderModel.orderNo());
        orderBankFacadeRequest.setAccountNo(StringUtils.isNotBlank(expressOrderModel.getCustomer().getAccountNo()) ? expressOrderModel.getCustomer().getAccountNo()
                : null != snapshot ? (StringUtils.isNotBlank(snapshot.getCustomer().getAccountNo()) ? snapshot.getCustomer().getAccountNo() : ("")) : "");
        return orderBankFacadeRequest;
    }

    /**
     * 收件人信息(拼接原单新单组合)
     *
     * @param expressOrderModel
     * @return
     */
    private ConsigneeInfo toConsigneeInfo(ExpressOrderModel expressOrderModel) {
        if (expressOrderModel == null) {
            return null;
        }
        ConsigneeInfo consigneeInfo = new ConsigneeInfo();
        Consignee consigneeNew = expressOrderModel.getConsignee();
        //原单为空直接赋值新单, 逆向单改址单使用新单数据
        if (expressOrderModel.getOrderSnapshot() == null || OrderTypeEnum.RETURN_ORDER == expressOrderModel.getOrderType()
                || OrderTypeEnum.READDRESS == expressOrderModel.getOrderType()) {
            consigneeInfo.setConsigneeName(consigneeNew.getConsigneeName());
            consigneeInfo.setConsigneeMobile(consigneeNew.getConsigneeMobile());
            consigneeInfo.setConsigneePhone(consigneeNew.getConsigneePhone());
            consigneeInfo.setConsigneeAddress(getLeftString(consigneeNew.getConsigneeFullAddress(), 60));
        } else {
            //优先赋值新单
            Consignee consigneeOld = expressOrderModel.getOrderSnapshot().getConsignee();
            consigneeInfo.setConsigneeName(StringUtils.isNotBlank(consigneeNew.getConsigneeName()) ? consigneeNew.getConsigneeName() : consigneeOld.getConsigneeName());
            consigneeInfo.setConsigneeMobile(StringUtils.isNotBlank(consigneeNew.getConsigneeMobile()) ? consigneeNew.getConsigneeMobile() : consigneeOld.getConsigneeMobile());
            consigneeInfo.setConsigneePhone(StringUtils.isNotBlank(consigneeNew.getConsigneePhone()) ? consigneeNew.getConsigneePhone() : consigneeOld.getConsigneePhone());
            consigneeInfo.setConsigneeAddress(StringUtils.isNotBlank(consigneeNew.getConsigneeFullAddress()) ?
                    getLeftString(consigneeNew.getConsigneeFullAddress(), 60) : getLeftString(consigneeOld.getConsigneeFullAddress(), 60));
        }
        return consigneeInfo;
    }

    /**
     * todo 确认限制60
     * 询价场景收件信息
     * 与接单场景toConsigneeInfo差别：直接使用orderSnapshot信息，不用当前单（询价不传收发信息）
     */
    private ConsigneeInfo toEnquirySceneConsigneeInfo(ExpressOrderModel orderSnapshot) {
        ConsigneeInfo consigneeInfo = new ConsigneeInfo();
        Consignee consignee = orderSnapshot.getConsignee();
        consigneeInfo.setConsigneeName(consignee.getConsigneeName());
        consigneeInfo.setConsigneeMobile(consignee.getConsigneeMobile());
        consigneeInfo.setConsigneePhone(consignee.getConsigneePhone());
        consigneeInfo.setConsigneeAddress(getLeftString(consignee.getConsigneeFullAddress(), 60));
        return consigneeInfo;
    }

    private String getLeftString(String src, int len) {
        if (src == null) {
            return null;
        }
        if (src.length() <= len) {
            return src;
        }
        return src.subSequence(0, len).toString();
    }

    /**
     * 发件人信息(拼接原单新单组合)
     *
     * @param expressOrderModel
     * @return
     */
    public OrderBankFacadeRequest.ConsignorInfo toConsignorInfo(ExpressOrderModel expressOrderModel) {
        if (expressOrderModel == null) {
            return null;
        }
        OrderBankFacadeRequest.ConsignorInfo consignorInfo = new OrderBankFacadeRequest.ConsignorInfo();
        Consignor consignorNew = expressOrderModel.getConsignor();
        //原单为空直接赋值新单, 逆向单改址单使用新单数据
        if (expressOrderModel.getOrderSnapshot() == null || OrderTypeEnum.RETURN_ORDER == expressOrderModel.getOrderType()
                || OrderTypeEnum.READDRESS == expressOrderModel.getOrderType()) {
            consignorInfo.setConsignorName(consignorNew.getConsignorName());
            consignorInfo.setConsignorMobile(consignorNew.getConsignorMobile());
            consignorInfo.setConsignorPhone(consignorNew.getConsignorPhone());
            consignorInfo.setConsignorAddress(consignorNew.getConsignorFullAddress());
            if (consignorNew.getAddress() != null) {
                consignorInfo.setConsignorProvinceNo(consignorNew.getAddress().getProvinceNoGis());
                consignorInfo.setConsignorProvinceName(consignorNew.getAddress().getProvinceNameGis());
                consignorInfo.setConsignorCityNo(consignorNew.getAddress().getCityNoGis());
                consignorInfo.setConsignorCityName(consignorNew.getAddress().getCityNameGis());
            }
        } else {
            //优先赋值新单
            Consignor consignorOld = expressOrderModel.getOrderSnapshot().getConsignor();
            consignorInfo.setConsignorName(StringUtils.isNotBlank(consignorNew.getConsignorName()) ? consignorNew.getConsignorName() : consignorOld.getConsignorName());
            consignorInfo.setConsignorMobile(StringUtils.isNotBlank(consignorNew.getConsignorMobile()) ? consignorNew.getConsignorName() : consignorOld.getConsignorMobile());
            consignorInfo.setConsignorPhone(StringUtils.isNotBlank(consignorNew.getConsignorPhone()) ? consignorNew.getConsignorPhone() : consignorOld.getConsignorPhone());
            consignorInfo.setConsignorAddress(StringUtils.isNotBlank(consignorNew.getConsignorFullAddress()) ? consignorNew.getConsignorFullAddress() : consignorOld.getConsignorFullAddress());
            if (consignorNew.getAddress() != null) {
                consignorInfo.setConsignorProvinceNo(consignorNew.getAddress().getProvinceNoGis());
                consignorInfo.setConsignorProvinceName(consignorNew.getAddress().getProvinceNameGis());
                consignorInfo.setConsignorCityNo(consignorNew.getAddress().getCityNoGis());
                consignorInfo.setConsignorCityName(consignorNew.getAddress().getCityNameGis());
            } else if (consignorOld.getAddress() != null) {
                consignorInfo.setConsignorProvinceNo(consignorOld.getAddress().getProvinceNoGis());
                consignorInfo.setConsignorProvinceName(consignorOld.getAddress().getProvinceNameGis());
                consignorInfo.setConsignorCityNo(consignorOld.getAddress().getCityNoGis());
                consignorInfo.setConsignorCityName(consignorOld.getAddress().getCityNameGis());
            }
        }
        return consignorInfo;
    }

    /**
     * 询价场景发件信息
     * 与接单场景toConsignorInfo差别：直接使用orderSnapshot信息，不用当前单（询价不传收发信息）；使用保存在上下文中的地址信息
     */
    private OrderBankFacadeRequest.ConsignorInfo toEnquirySceneConsignorInfo(ExpressOrderContext expressOrderContext, ExpressOrderModel orderSnapshot) {
        Consignor consignor = orderSnapshot.getConsignor();

        OrderBankFacadeRequest.ConsignorInfo consignorInfo = new OrderBankFacadeRequest.ConsignorInfo();
        consignorInfo.setConsignorName(consignor.getConsignorName());
        consignorInfo.setConsignorMobile(consignor.getConsignorMobile());
        consignorInfo.setConsignorPhone(consignor.getConsignorPhone());

        // 地址信息和询价节点保持一致
        Object startStationAddress = expressOrderContext.getExtInfo(ContextInfoEnum.START_STATION_ADDRESS.getCode());
        if (startStationAddress != null && startStationAddress instanceof AddressBasicPrimaryWSFacadeResponse) {
            AddressBasicPrimaryWSFacadeResponse addressBasicPrimaryWSFacadeResponse = (AddressBasicPrimaryWSFacadeResponse) startStationAddress;
            LOGGER.info("台账节点AddressBasicPrimaryWSFacadeResponse存在，AddressBasicPrimaryWSFacadeResponse={}", addressBasicPrimaryWSFacadeResponse);
            consignorInfo.setConsignorAddress(addressBasicPrimaryWSFacadeResponse.getAddress());
            consignorInfo.setConsignorProvinceNo(addressBasicPrimaryWSFacadeResponse.getProvinceId());
            consignorInfo.setConsignorProvinceName(addressBasicPrimaryWSFacadeResponse.getProvinceName());
            consignorInfo.setConsignorCityNo(addressBasicPrimaryWSFacadeResponse.getCityId());
            consignorInfo.setConsignorCityName(addressBasicPrimaryWSFacadeResponse.getCityName());
        } else {
            consignorInfo.setConsignorAddress(consignor.getConsignorFullAddress());
            if (consignor.getAddress() != null) {
                consignorInfo.setConsignorProvinceNo(consignor.getAddress().getProvinceNoGis());
                consignorInfo.setConsignorProvinceName(consignor.getAddress().getProvinceNameGis());
                consignorInfo.setConsignorCityNo(consignor.getAddress().getCityNoGis());
                consignorInfo.setConsignorCityName(consignor.getAddress().getCityNameGis());
            }
        }

        return consignorInfo;
    }

    /**
     * 逆向改址单，异步写台账的请求转换
     *
     * @param expressOrderContext
     * @return
     */
    public OrderBankFacadeMiddleRequest toReverseReAddressOrderBankFacadeRequest(ExpressOrderContext expressOrderContext) {
        OrderBankFacadeMiddleRequest orderBankFacadeMiddleRequest = new OrderBankFacadeMiddleRequest();

        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);
        Integer taxSettlementType = GetFieldUtils.getTaxSettlementType(expressOrderContext);
        // B 商家创建下面分支中都有用到，所以提到最外层
        OrderBankFacadeRequest.BMerchantModify bMerchantJfModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.BMerchantModify bMerchantCodModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.PosJfYun posJfYun = new OrderBankFacadeRequest.PosJfYun();
        OrderBankFacadeRequest.PosYun posDfYun = new OrderBankFacadeRequest.PosYun();
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();


        // 寄付月结：结算方式为月结的，若有代收货款则取代收货款赋值到POS到付台账和B商家台账的应收金额
        if (SettlementTypeEnum.MONTHLY_PAYMENT.equals(settlementType)) {
            //代收货款
            BigDecimal codMoney = getCodMoney(expressOrderContext);
            posJfYun = null;
            bMerchantJfModify = null;
            bMerchantDfModify = null;

            if (codMoney == null || BigDecimal.ZERO.compareTo(codMoney) == 0) {
                bMerchantCodModify = null;
                posDfYun = null;
            } else {
                //B商家cod
                OrderBankFacadeRequest.BMerchantDueDetailInfo codDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                codDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
                codDueDetailInfo.setAmount(codMoney);
                bMerchantCodModify.setBMerchantDueDetailInfo(codDueDetailInfo);

                //pos到付
                posDfYun.setPosType(POS_TYPE);
                posDfYun.setAmount(codMoney);
                //pos到付wayBillSign
                posDfYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, null, orderModel.getFinance(), settlementType, true)));
            }
        }
        else if (SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)) { // 非月结的POS寄付：取订单财务的折后金额
            //寄付运费
            BigDecimal discountMoney = GetFieldUtils.getDiscountMoney(orderModel);
            //代收货款
            BigDecimal codMoney = getCodMoney(expressOrderContext);
            if (codMoney == null) {
                codMoney = BigDecimal.ZERO;
            }
            if (discountMoney == null) {
                discountMoney = BigDecimal.ZERO;
            }

            bMerchantDfModify = null;

            posJfYun.setAmount(discountMoney);
            posJfYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, null, orderModel.getFinance(), settlementType, false)));
            posJfYun.setBusinessNo(POS_JF);

            OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfoJf = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            bMerchantDueDetailInfoJf.setAmount(discountMoney);
            bMerchantDueDetailInfoJf.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
            bMerchantJfModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfoJf);


            if (BigDecimal.ZERO.compareTo(codMoney) != 0) {
                OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfoCod = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                bMerchantDueDetailInfoCod.setAmount(codMoney);
                bMerchantDueDetailInfoCod.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
                bMerchantCodModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfoCod);

                posDfYun.setAmount(codMoney);
                posDfYun.setPosType(POS_TYPE);
                //pos到付wayBillSign
                posDfYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, null, orderModel.getFinance(), settlementType, true)));
            } else {
                bMerchantCodModify = null;
                posDfYun = null;
            }
        }
        else if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)) { // 非月结的POS到付：取订单财务的折后金额+代收货款(若有)
            //到付运费
            Money discountMoney = GetFieldUtils.getDiscount(orderModel);
            //代收货款
            BigDecimal codMoney = getCodMoney(expressOrderContext);

            // B商家货款cod
            if (codMoney == null || BigDecimal.ZERO.compareTo(codMoney) == 0) {
                bMerchantCodModify = null;
            } else {
                OrderBankFacadeRequest.BMerchantDueDetailInfo codDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                codDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
                codDueDetailInfo.setAmount(codMoney);
                bMerchantCodModify.setBMerchantDueDetailInfo(codDueDetailInfo);
            }

            //B商家到付运费
            if (discountMoney == null) {
                bMerchantDfModify = null;
            } else {
                OrderBankFacadeRequest.BMerchantDueDetailInfo dfDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                dfDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
                dfDueDetailInfo.setAmount(discountMoney.getAmount());
                dfDueDetailInfo.setCurrency(String.valueOf(discountMoney.getCurrency().getFeeSystemCode()));
                bMerchantDfModify.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderModel));
                bMerchantDfModify.setBMerchantDueDetailInfo(dfDueDetailInfo);
            }

            //B商家寄付运费
            bMerchantJfModify = null;
            //pos寄付
            posJfYun = null;

            //pos到付
            if (codMoney == null) {
                codMoney = BigDecimal.ZERO;
            }

            posDfYun.setPosType(POS_TYPE);
            posDfYun.setAmount(codMoney.add(discountMoney.getAmount()));
            posDfYun.setCurrency(String.valueOf(discountMoney.getCurrency().getFeeSystemCode()));
            //pos到付wayBillSign
            posDfYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, null, orderModel.getFinance(), settlementType, true)));

        }

        //fixme 港澳税金处理
        Finance finance = orderModel.getFinance();
        if(needTaxOrderBankStation(orderModel.getShipment().getEndStationNo())
                && null != finance && null != finance.getEstimatedTax()
                && null != finance.getEstimatedTax().getAmount()
                && finance.getEstimatedTax().getAmount().compareTo(BigDecimal.ZERO) > 0){
            orderBankFacadeMiddleRequest.setTaxBankServiceOrderNo("TAX"+orderModel.getRefOrderInfoDelegate().getWaybillNo());
            orderModel.getFinance().complementTaxBankServiceOrderNo("TAX"+orderModel.getRefOrderInfoDelegate().getWaybillNo());
            CustomerConfig customerConfig = getCustomerConfig(orderModel);
            if(Objects.equals(SettlementTypeEnum.CASH_ON_DELIVERY.getCode(), taxSettlementType)){
                OrderBankFacadeRequest.PosYun posYunTax = new OrderBankFacadeRequest.PosYun();
                posYunTax.setAmount(finance.getEstimatedTax().getAmount());
                posYunTax.setPosType(POS_TYPE);
                posYunTax.setWayBillSign(String.valueOf(getWaybillSign(orderModel, null, orderModel.getFinance(), settlementType, true)));
                posYunTax.setCurrency(String.valueOf(finance.getEstimatedTax().getCurrency().getFeeSystemCode()));
                orderBankFacadeMiddleRequest.setPosYunTax(posYunTax);

                //B商家  到付
                OrderBankFacadeRequest.BMerchantCreate bMerchantTaxCreate = new OrderBankFacadeRequest.BMerchantCreate();
                List<OrderBankFacadeRequest.BMerchantDueDetailInfo> bMerchantDueDetailInfoList = new ArrayList<>();
                OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                bMerchantDueDetailInfo.setAmount(finance.getEstimatedTax().getAmount());
                bMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_TAX_FEE);
                bMerchantDueDetailInfoList.add(bMerchantDueDetailInfo);
                bMerchantTaxCreate.setOtsMerchantId(MerchantEnum.TAX.getMerchantId());
                bMerchantTaxCreate.setBMerchantDueDetailInfos(bMerchantDueDetailInfoList);
                bMerchantTaxCreate.setAmount(finance.getEstimatedTax().getAmount());
                bMerchantTaxCreate.setCurrency(String.valueOf(finance.getEstimatedTax().getCurrency().getFeeSystemCode()));
                bMerchantTaxCreate.setDataSources(B_MERCHANT_CREATE_DATA_SOURCE_TAX);
                bMerchantTaxCreate.setSellerId(String.valueOf(customerConfig.getCustomerId()));
                bMerchantTaxCreate.setSellerName(customerConfig.getCustomerName());
                orderBankFacadeMiddleRequest.setBMerchantTaxCreate(bMerchantTaxCreate);
            }
        }
        orderBankFacadeMiddleRequest.setBMerchantCodModify(bMerchantCodModify);
        orderBankFacadeMiddleRequest.setBMerchantJfModify(bMerchantJfModify);
        orderBankFacadeMiddleRequest.setBMerchantDfModify(bMerchantDfModify);
        orderBankFacadeMiddleRequest.setPosJfYun(posJfYun);
        orderBankFacadeMiddleRequest.setPosYun(posDfYun);
        return orderBankFacadeMiddleRequest;
    }

    /**
     * 单据回传，调整台账，白条预授权，微信代扣的更新外单台账时，需要清除B商家台账和POS台账
     *
     * @param expressOrderContext
     * @return
     */
    @Deprecated
    public OrderBankFacadeMiddleRequest toAdjustOrderBankRequest(ExpressOrderContext expressOrderContext) {
        OrderBankFacadeMiddleRequest orderBankFacadeMiddleRequest = new OrderBankFacadeMiddleRequest();

        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);
        // B 商家创建下面分支中都有用到，所以提到最外层
        OrderBankFacadeRequest.BMerchantModify bMerchantJfModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.PosJfYun posJfYun = new OrderBankFacadeRequest.PosJfYun();
        OrderBankFacadeRequest.PosYun posDfYun = new OrderBankFacadeRequest.PosYun();
        //外单台账, 这里的快照指的是当前单!!!
        OrderBankFacadeRequest.OtsCreate otsCreate = toOtsCreate(expressOrderContext.getOrderModel().getOrderSnapshot());

        //代收货款
        BigDecimal codMoney = BigDecimal.ZERO;
        Product product = expressOrderContext.getOrderModel().getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.JDL_COD_TOC.getCode());
        if (product != null) {
            codMoney = TypeConversion.stringToBigDecimal(product.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()), DEFAULT_AMOUNT_DECIMAL_SCALE, null);
        }

        // 寄付月结：结算方式为月结的，若有代收货款则取代收货款赋值到POS到付台账和B商家台账的应收金额
        if (SettlementTypeEnum.MONTHLY_PAYMENT.equals(settlementType)) {
            posJfYun = null;
            bMerchantJfModify = null;
            bMerchantDfModify = null;

            if (codMoney == null || BigDecimal.ZERO.compareTo(codMoney) == 0) {
                posDfYun = null;
            } else {
                //pos到付
                posDfYun.setPosType(POS_TYPE);
                posDfYun.setAmount(codMoney);
                //pos到付wayBillSign
                posDfYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), null, expressOrderContext.getOrderModel().getFinance(), settlementType, true)));
            }
        } else if (SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)) { // 非月结的POS寄付：取订单财务的折后金额
            //寄付运费
            BigDecimal discountMoney = GetFieldUtils.getDiscountMoney(expressOrderContext.getOrderModel());

            if (codMoney == null) {
                codMoney = BigDecimal.ZERO;
            }
            if (discountMoney == null) {
                discountMoney = BigDecimal.ZERO;
            }

            bMerchantDfModify = null;

            if (BigDecimal.ZERO.compareTo(discountMoney) != 0) {
                posJfYun.setAmount(BigDecimal.ZERO);
                posJfYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), expressOrderContext.getOrderModel().getOrderSnapshot(), expressOrderContext.getOrderModel().getOrderSnapshot().getFinance(), settlementType, false)));
                posJfYun.setBusinessNo(POS_JF);

                OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfoJf = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                bMerchantDueDetailInfoJf.setAmount(BigDecimal.ZERO);
                bMerchantDueDetailInfoJf.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
                bMerchantJfModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfoJf);
            } else {
                posJfYun = null;
                bMerchantJfModify = null;
            }

            if (BigDecimal.ZERO.compareTo(codMoney) != 0) {
                posDfYun.setAmount(codMoney);
                posDfYun.setPosType(POS_TYPE);
                //pos到付wayBillSign
                posDfYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), expressOrderContext.getOrderModel().getOrderSnapshot(), expressOrderContext.getOrderModel().getOrderSnapshot().getFinance(), settlementType, true)));
            } else {
                posDfYun = null;
            }
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)) { // 非月结的POS到付：取订单财务的折后金额+代收货款(若有)
            //到付运费
            BigDecimal discountMoney = GetFieldUtils.getDiscountMoney(expressOrderContext.getOrderModel());

            //B商家到付运费
            if (discountMoney == null || BigDecimal.ZERO.compareTo(discountMoney) == 0) {
                bMerchantDfModify = null;
            } else {
                OrderBankFacadeRequest.BMerchantDueDetailInfo dfDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                dfDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
                dfDueDetailInfo.setAmount(BigDecimal.ZERO);
                bMerchantDfModify.setBMerchantDueDetailInfo(dfDueDetailInfo);
            }

            //B商家寄付运费
            bMerchantJfModify = null;
            //pos寄付
            posJfYun = null;

            //pos到付
            if (codMoney == null) {
                codMoney = BigDecimal.ZERO;
            }
            if (discountMoney == null) {
                discountMoney = BigDecimal.ZERO;
            }
            if (BigDecimal.ZERO.compareTo(discountMoney) == 0) {
                posDfYun = null;
            } else {
                posDfYun.setPosType(POS_TYPE);
                posDfYun.setAmount(codMoney);
                //pos到付wayBillSign
                posDfYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), null, expressOrderContext.getOrderModel().getFinance(), settlementType, true)));
            }
        }
        orderBankFacadeMiddleRequest.setBMerchantCodModify(null);
        orderBankFacadeMiddleRequest.setBMerchantJfModify(bMerchantJfModify);
        orderBankFacadeMiddleRequest.setBMerchantDfModify(bMerchantDfModify);
        orderBankFacadeMiddleRequest.setPosJfYun(posJfYun);
        orderBankFacadeMiddleRequest.setPosYun(posDfYun);
        orderBankFacadeMiddleRequest.setOtsCreate(otsCreate);

        return orderBankFacadeMiddleRequest;
    }

    /**
     * 先款订单原单外单台账
     * 先款订单的改址（若原单的结算方式是到付现结，则原单需要获取费用信息写外单台账（无需询价）（用于小程序合并支付））
     *
     * @param expressOrderContext
     */
    public OrderBankFacadeRequest toOtsCreateForOldOrder(ExpressOrderContext expressOrderContext) {
        OrderBankFacadeRequest orderBankFacadeRequest = null;
        Finance finance = expressOrderContext.getOrderModel().getFinance();
        ExpressOrderModel orderSnapshot = expressOrderContext.getOrderModel().getOrderSnapshot();
        if (finance == null || orderSnapshot == null) {
            return null;
        }
        Finance financeOld = orderSnapshot.getFinance();
        //新单需要是先款订单
        if (PaymentStageEnum.ONLINEPAYMENT.equals(finance.getPaymentStage())) {
            //原单到付现结
            if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(orderSnapshot.getFinance().getSettlementType())) {
                orderBankFacadeRequest = toCommonOrderBankFacadeRequest(orderSnapshot, expressOrderContext.getOrderModel().requestProfile().getTenantId());
                orderBankFacadeRequest.setOrgId(financeOld.getCollectionOrgNo());
                orderBankFacadeRequest.setWaybillNo(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());
                OrderBankFacadeRequest.OtsCreate otsCreate = toOtsCreate(orderSnapshot);
                orderBankFacadeRequest.setOtsCreate(otsCreate);
            }
        }
        return orderBankFacadeRequest;
    }

    public OrderBankFacadeRequest.OtsCreate toOtsCreate(ExpressOrderModel orderModel) {
        // TODO @liujiangwai1 merchant 判断改到通用方法
        MerchantEnum merchantEnum = MerchantEnum.ONLINE_READDRESS;
        String mainProductNo = orderModel.getProductDelegate().getMajorProductNo();
        if (YES_VAL.equals(MapUtils.getString(orderModel.getOrderSign(), OrderSignEnum.DOCUMENT_SEND.getCode()))) {
            merchantEnum = MerchantEnum.DOCUMENT_DELIVERY;
        } else if (ProductEnum.KCJS.getCode().equals(mainProductNo) || ProductEnum.TSSTC.getCode().equals(mainProductNo)) {
            merchantEnum = MerchantEnum.O2O_C;
        }
        return orderBankFacadeTranslator.toOtsCreate(orderModel, merchantEnum);
    }

    /**
     * 先款订单改址单新单外单台账
     *
     * @param expressOrderModel
     * @return
     */
    public OrderBankFacadeRequest.OtsCreate toOtsCreateForNewOrder(ExpressOrderModel expressOrderModel) {
        // todo 先款改址单：区分如果原单到付需要合并费用
        return toOtsCreate(expressOrderModel);
    }

    /**
     * Pos到付构建
     */
    public OrderBankFacadeRequest.PosYun toPosYun(ExpressOrderModel expressOrderModel, ExpressOrderModel snapshot, BigDecimal amount, SettlementTypeEnum settlementType) {
        OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
        posYun.setPosType(C2COrderBankFacadeTranslator.POS_TYPE);
        posYun.setAmount(amount);
        posYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderModel, snapshot, expressOrderModel.getFinance(), settlementType, true)));
        return posYun;
    }

    public OrderBankFacadeRequest.PosJfYun toPosJfYun(ExpressOrderModel orderModel, ExpressOrderModel snapshot, BigDecimal amount, SettlementTypeEnum settlementType) {
        OrderBankFacadeRequest.PosJfYun posJfYun = new OrderBankFacadeRequest.PosJfYun();
        posJfYun.setAmount(amount);
        posJfYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, snapshot, orderModel.getFinance(), settlementType, false)));
        posJfYun.setBusinessNo(POS_JF);
        return posJfYun;
    }

    /**
     * 生成0 pos到付
     */
    public OrderBankFacadeRequest.PosYun generateZeroPosYun(ExpressOrderModel expressOrderModel, SettlementTypeEnum settlementType) {
        OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
        posYun.setPosType(C2COrderBankFacadeTranslator.POS_TYPE);
        posYun.setAmount(BigDecimal.ZERO);
        posYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderModel, null, expressOrderModel.getFinance(), settlementType, true)));
        return posYun;
    }


    /**
     * 生成0 B商家
     */
    public OrderBankFacadeRequest.BMerchantDueDetailInfo generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum receiveTypeEnum) {
        OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
        bMerchantDueDetailInfo.setReceiveType(receiveTypeEnum);
        bMerchantDueDetailInfo.setAmount(BigDecimal.ZERO);
        return bMerchantDueDetailInfo;
    }

    /**
     * 补齐支付单号
     *
     * @param expressOrderContext
     * @param orderBankFacadeResponse
     */
    public void complementPaymentNo(ExpressOrderContext expressOrderContext,
                                    OrderBankFacadeResponse orderBankFacadeResponse) {
        if (orderBankFacadeResponse == null || StringUtils.isBlank(orderBankFacadeResponse.getPosPayAppNo())) {
            return;
        }
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setPaymentNo(orderBankFacadeResponse.getPosPayAppNo());
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementFinanceInfoPaymentNo(this, expressOrderModelCreator);
    }

    /**
     * 改址单获取cod
     *
     * @param expressOrderContext
     * @return
     */
    private BigDecimal getCodMoney(ExpressOrderContext expressOrderContext) {
        Product product = expressOrderContext.getOrderModel().getProductDelegate().ofProductNo(AddOnProductEnum.JDL_COD_TOC.getCode());
        if (product != null) {
            return TypeConversion.stringToBigDecimal(product.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()), DEFAULT_AMOUNT_DECIMAL_SCALE, null);
        }
        return null;
    }

    /**
     * 先款订单(B商家和pos到付写cod，pos寄付写0)
     *
     * @param expressOrderContext
     * @param requestProfile
     * @return
     */
    public OrderBankFacadeRequest toCreateOnlinePaymentOrderBankFacadeRequest(ExpressOrderContext expressOrderContext, RequestProfile requestProfile) {
        OrderBankFacadeRequest orderBankFacadeRequest = toCommonOrderBankFacadeRequest(expressOrderContext.getOrderModel(), requestProfile.getTenantId());

        // 判断COD，先款单无COD不写B商家
        BigDecimal cod = GetFieldUtils.getCod(expressOrderContext);
        if (null == cod || BigDecimal.ZERO.compareTo(cod) >= 0) {
            LOGGER.info("订单:{} 无COD，不写B商家和POS", expressOrderContext.getOrderModel().orderNo());
            return orderBankFacadeRequest;
        }

        // B 商家创建下面分支中都有用到，所以提到最外层
        OrderBankFacadeRequest.BMerchantCreate bMerchantCreate = new OrderBankFacadeRequest.BMerchantCreate();

        bMerchantCreate.setDataSources(B_MERCHANT_CREATE_DEFAULT_DATA_SOURCES);
        if (expressOrderContext.getCustomerConfig() != null) {
            bMerchantCreate.setSellerId(String.valueOf(expressOrderContext.getCustomerConfig().getCustomerId()));
            bMerchantCreate.setSellerName(expressOrderContext.getCustomerConfig().getCustomerName());
        }
        // 这里肯定有cod
        bMerchantCreate.setPayMode(PayModeEnum.COD);
        bMerchantCreate.setAmount(cod);

        List<OrderBankFacadeRequest.BMerchantDueDetailInfo> dueDetailInfos = new ArrayList<>();
        bMerchantCreate.setBMerchantDueDetailInfos(dueDetailInfos);
        orderBankFacadeRequest.setBMerchantCreate(bMerchantCreate);

        //B商家三项明细
        // B商家详情-寄付
        OrderBankFacadeRequest.BMerchantDueDetailInfo dueDetailInfoJf = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
        dueDetailInfos.add(dueDetailInfoJf);
        // B商家详情-到付
        OrderBankFacadeRequest.BMerchantDueDetailInfo dueDetailInfoDf = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
        dueDetailInfos.add(dueDetailInfoDf);
        // B商家详情-cod
        OrderBankFacadeRequest.BMerchantDueDetailInfo dueDetailInfoCod = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
        dueDetailInfoCod.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
        dueDetailInfoCod.setAmount(cod);
        dueDetailInfos.add(dueDetailInfoCod);

        // 如果Cod大于0，则写pos到付
        OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
        posYun.setPosType(POS_TYPE);
        //pos到付wayBillSign
        posYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), null, expressOrderContext.getOrderModel().getFinance(), expressOrderContext.getOrderModel().getFinance().getSettlementType(), true)));
        posYun.setAmount(cod);
        orderBankFacadeRequest.setPosYun(posYun);

        return orderBankFacadeRequest;
    }

    private OrderBankFacadeRequest.BMerchantCreate initBMerchantCreate(ExpressOrderContext context, BigDecimal codMoney) {
        // B 商家创建下面分支中都有用到，所以提到最外层
        OrderBankFacadeRequest.BMerchantCreate bMerchantCreate = new OrderBankFacadeRequest.BMerchantCreate();
        // B商家费用明细
        List<OrderBankFacadeRequest.BMerchantDueDetailInfo> dueDetailInfos = new ArrayList<>();
        bMerchantCreate.setBMerchantDueDetailInfos(dueDetailInfos);
        // B商家数据来源
        if(context.getOrderModel().isHKMO()) {
            bMerchantCreate.setDataSources(B_MERCHANT_CREATE_DATA_SOURCES_LDOP_HM);
        } else {
            bMerchantCreate.setDataSources(B_MERCHANT_CREATE_DEFAULT_DATA_SOURCES);
        }
        // B商家 商家信息
        if (context.getCustomerConfig() != null) {
            bMerchantCreate.setSellerId(String.valueOf(context.getCustomerConfig().getCustomerId()));
            bMerchantCreate.setSellerName(context.getCustomerConfig().getCustomerName());
        }
        // 根据 **代收货款** 判读支付模式
        if (BigDecimal.ZERO.compareTo(codMoney) == 0) { //有代收货款增值服务则赋值1
            bMerchantCreate.setPayMode(PayModeEnum.ONLINE);
        } else {
            bMerchantCreate.setPayMode(PayModeEnum.COD);
        }
        return bMerchantCreate;
    }

    /**
     * 补齐询价状态
     *
     * @param expressOrderContext
     */
    public void complementEnquiryStatus(ExpressOrderContext expressOrderContext, EnquiryStatusEnum enquiryStatus) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setEnquiryStatus(enquiryStatus);
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementEnquiryStatus(this, expressOrderModelCreator);
    }

    /**
     * 询价场景转换台账请求
     */
    public OrderBankFacadeMiddleRequest toEnquirySceneOrderBankFacadeRequest(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);
        // 获取询价后的折后总金额 当前单不存在取原单的
        BigDecimal discountAmount = GetFieldUtils.getDiscountMoney(orderModel);
        BigDecimal codMoney = GetFieldUtils.getCod(expressOrderContext);

        OrderBankFacadeMiddleRequest orderBankFacadeRequest = new OrderBankFacadeMiddleRequest();
        OrderBankFacadeRequest.BMerchantModify bMerchantJfModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.PosJfYun posJfYun =  new OrderBankFacadeRequest.PosJfYun();
        OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
        orderBankFacadeRequest.setWaybillNo(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());
        orderBankFacadeRequest.setOrgId(orderModel.getFinance().getCollectionOrgNo());
        orderBankFacadeRequest.setOrgName(orderModel.getFinance().getCollectionOrgName());
        orderBankFacadeRequest.setUUid(orderModel.requestProfile().getTenantId() + "_" + orderModel.orderNo());
        orderBankFacadeRequest.setConsigneeInfo(toEnquirySceneConsigneeInfo(orderSnapshot));
        orderBankFacadeRequest.setConsignorInfo(toEnquirySceneConsignorInfo(expressOrderContext, orderSnapshot));

        //fixme 结算方式修改 寄付改到付把寄付清0；到付改寄付把到付清0
        if (SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)) { // 寄付现结：取订单财务的折后金额
            //寄付现结的情况下
            // pos到付 取订单财务的折后金额+代收货款(若有)
            // fixme 当前询价只有港澳业务 终端调用 没有COD，目前为0
            posYun.setAmount(codMoney);
            posYun.setPosType(POS_TYPE);
            posYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, orderSnapshot, orderModel.getFinance(), settlementType, true)));
            orderBankFacadeRequest.setPosYun(posYun);

            //B商家  到付
            OrderBankFacadeRequest.BMerchantDueDetailInfo dfBMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            dfBMerchantDueDetailInfo.setAmount(BigDecimal.ZERO);
            dfBMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
            bMerchantDfModify.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderSnapshot));
            bMerchantDfModify.setBMerchantDueDetailInfo(dfBMerchantDueDetailInfo);
            orderBankFacadeRequest.setBMerchantDfModify(bMerchantDfModify);

            if (discountAmount != null) {
                // pos寄付
                posJfYun.setAmount(discountAmount);
                posJfYun.setBusinessNo(POS_JF);
                posJfYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, orderSnapshot, orderModel.getFinance(), settlementType, false)));
                orderBankFacadeRequest.setPosJfYun(posJfYun);

                //B商家  寄付
                OrderBankFacadeRequest.BMerchantDueDetailInfo jfBMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                jfBMerchantDueDetailInfo.setAmount(discountAmount);
                jfBMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
                bMerchantJfModify.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderSnapshot));
                bMerchantJfModify.setBMerchantDueDetailInfo(jfBMerchantDueDetailInfo);
                orderBankFacadeRequest.setBMerchantJfModify(bMerchantJfModify);

                // 芝麻代扣 写外单台账
                if (PaymentTypeEnum.ifAliPay(orderSnapshot.getFinance().getPayment())) {
                    Money money = new Money();
                    // 待支付金额
                    money.setAmount(discountAmount);
                    money.setCurrency(CurrencyCodeEnum.of(orderSnapshot.getFinance().getDiscountAmount().getCurrency().getCode()));
                    OrderBankFacadeRequest.OtsCreate otsCreate = orderBankFacadeTranslator.toOtsCreate(orderSnapshot.getOperator(), money, MerchantEnum.SAN_DAN_JI_JIAN);
                    otsCreate.setCreateReplaceFlag(true);
                    orderBankFacadeRequest.setOtsCreate(otsCreate);
                }
            }


        } else if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)) { // 到付
            // pos寄付
            posJfYun.setAmount(BigDecimal.ZERO);
            posJfYun.setBusinessNo(POS_JF);
            posJfYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, orderSnapshot, orderModel.getFinance(), settlementType, false)));
            orderBankFacadeRequest.setPosJfYun(posJfYun);

            //B商家  寄付
            OrderBankFacadeRequest.BMerchantDueDetailInfo jfBMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            jfBMerchantDueDetailInfo.setAmount(BigDecimal.ZERO);
            jfBMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
            bMerchantJfModify.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderSnapshot));
            bMerchantJfModify.setBMerchantDueDetailInfo(jfBMerchantDueDetailInfo);
            orderBankFacadeRequest.setBMerchantJfModify(bMerchantJfModify);

            // fixme 当前询价只有港澳业务 终端调用 没有COD，目前为0
            BigDecimal allAmount = getTotalMoney(discountAmount, codMoney);
            if (allAmount != null) {
                // pos到付 取订单财务的折后金额+代收货款(若有)
                posYun.setAmount(allAmount);
                posYun.setPosType(POS_TYPE);
                posYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, orderSnapshot, orderModel.getFinance(), settlementType, true)));
                orderBankFacadeRequest.setPosYun(posYun);
            }
            if (discountAmount != null) {
                //B商家  到付
                OrderBankFacadeRequest.BMerchantDueDetailInfo dfBMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                dfBMerchantDueDetailInfo.setAmount(discountAmount);
                dfBMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
                bMerchantDfModify.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderSnapshot));
                bMerchantDfModify.setBMerchantDueDetailInfo(dfBMerchantDueDetailInfo);
                orderBankFacadeRequest.setBMerchantDfModify(bMerchantDfModify);
            }
        }

        // 处理币种
        OrderBankCurrencyUtil.handleCurrency(expressOrderContext, orderBankFacadeRequest);

        // 处理国际出口的省信息
        OTSLedgerUtil.setOrderBankProvinceForIntlExport(orderSnapshot, orderBankFacadeRequest);

        return orderBankFacadeRequest;
    }

    /**
     * 获取客户信息
     * @param orderInfo
     * @return
     */
    private CustomerConfig getCustomerConfig(ExpressOrderModel orderInfo){
        CustomerConfig customerConfig = new CustomerConfig();
        String accountNo = orderInfo.getCustomer().getAccountNo();
        if (StringUtils.isNotBlank(accountNo)) {
            BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(accountNo);
            customerConfig.setTraderSign(basicTraderResponse.getTraderSign());
            customerConfig.setCustomerId(basicTraderResponse.getCustomerId());
            customerConfig.setCustomerName(basicTraderResponse.getCustomerName());
            customerConfig.setSignedCompany(basicTraderResponse.getSignedCompany());
            customerConfig.setSignedOrg(basicTraderResponse.getSignedOrg());
        }
        return customerConfig;
    }

    /**
     * 港澳税金灰度派送站点白名单校验
     * @param endStationNo
     * @return
     */
    private boolean needTaxOrderBankStation(String endStationNo){
        return StringUtils.isNotBlank(expressUccConfigCenter.getTaxPDAEndStationWhite())
                && ("ALL".equals(expressUccConfigCenter.getTaxPDAEndStationWhite())
                || expressUccConfigCenter.getTaxPDAEndStationWhiteSet().contains(endStationNo));
    }

    /**
     * 生成运费【无COD】pos到付
     */
    public OrderBankFacadeRequest.PosYun generateYFPosYun(ExpressOrderModel expressOrderModel, SettlementTypeEnum settlementType) {
        OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
        posYun.setPosType(C2COrderBankFacadeTranslator.POS_TYPE);
        // 默认运费0
        posYun.setAmount(BigDecimal.ZERO);
        // 从财务信息获取折后金额 大于0 则赋值
        Optional.ofNullable(expressOrderModel.getFinance().getDiscountAmount())
                .map(Money::getAmount)
                .filter(amount -> BigDecimal.ZERO.compareTo(amount) < 0)
                .ifPresent(posYun::setAmount);
        // waybill sign
        posYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderModel, null, expressOrderModel.getFinance(), settlementType, true)));
        return posYun;
    }


    /**
     * 当前单组装外单台账入参
     * @param orderSnapshot
     */
    public OrderBankFacadeRequest toOtsCreateFromOrder(ExpressOrderModel orderSnapshot, MerchantEnum merchantEnum, String tenantId) {
        OrderBankFacadeRequest orderBankFacadeRequest = null;
        Finance finance = orderSnapshot.getFinance();
        if (finance == null) {
            return null;
        }
        Money money = new Money();
        //待支付金额
        money.setAmount(finance.getDiscountAmount().getAmount());
        money.setCurrency(CurrencyCodeEnum.of(finance.getDiscountAmount().getCurrency().getCode()));

        if (null == money.getAmount() || money.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        orderBankFacadeRequest = this.toCommonOrderBankFacadeRequest(orderSnapshot, tenantId);
        OrderBankFacadeRequest.OtsCreate otsCreate = orderBankFacadeTranslator.toOtsCreate(orderSnapshot.getOperator(), money, merchantEnum);

        otsCreate.setCreateReplaceFlag(true);
        orderBankFacadeRequest.setOtsCreate(otsCreate);
        return orderBankFacadeRequest;
    }

    /**
     * 收件人信息(拼接原单新单组合)
     *
     * @param expressOrderModel
     * @return
     */
    public OrderBankFacadeRequest.ConsigneeInfo toConsigneeInfoFromOrder(ExpressOrderModel expressOrderModel) {
        if (expressOrderModel == null) {
            return null;
        }
        OrderBankFacadeRequest.ConsigneeInfo consigneeInfo = new OrderBankFacadeRequest.ConsigneeInfo();
        Consignee consignee = expressOrderModel.getConsignee();
        consigneeInfo.setConsigneeName(consignee.getConsigneeName());
        consigneeInfo.setConsigneeMobile(consignee.getConsigneeMobile());
        consigneeInfo.setConsigneePhone(consignee.getConsigneePhone());
        consigneeInfo.setConsigneeAddress(consignee.getConsigneeFullAddress());
        return consigneeInfo;
    }

    /**
     * 发件人信息(拼接原单新单组合)
     *
     * @param expressOrderModel
     * @return
     */
    public OrderBankFacadeRequest.ConsignorInfo toConsignorInfoFromOrder(ExpressOrderModel expressOrderModel) {
        if (expressOrderModel == null) {
            return null;
        }
        OrderBankFacadeRequest.ConsignorInfo consignorInfo = new OrderBankFacadeRequest.ConsignorInfo();
        Consignor consignor = expressOrderModel.getConsignor();
        consignorInfo.setConsignorName(consignor.getConsignorName());
        consignorInfo.setConsignorMobile(consignor.getConsignorMobile());
        consignorInfo.setConsignorPhone(consignor.getConsignorPhone());
        consignorInfo.setConsignorAddress(consignor.getConsignorFullAddress());
        if (consignor.getAddress() != null) {
            consignorInfo.setConsignorProvinceNo(consignor.getAddress().getProvinceNoGis());
            consignorInfo.setConsignorProvinceName(consignor.getAddress().getProvinceNameGis());
            consignorInfo.setConsignorCityNo(consignor.getAddress().getCityNoGis());
            consignorInfo.setConsignorCityName(consignor.getAddress().getCityNameGis());
        }
        return consignorInfo;
    }

    /**
     * 暂存自提单台账请求对象-接单场景
     *
     * @param expressOrderContext
     * @param requestProfile
     * @return
     */
    public OrderBankFacadeRequest toSelfPickupTemporaryStorageOrderBankFacadeRequest(ExpressOrderContext expressOrderContext, RequestProfile requestProfile) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        OrderBankFacadeRequest orderBankFacadeRequest = toCommonOrderBankFacadeRequest(orderModel, requestProfile.getTenantId());
        // B商家到付
        OrderBankFacadeRequest.BMerchantCreate bMerchantCreate = new OrderBankFacadeRequest.BMerchantCreate();
        List<OrderBankFacadeRequest.BMerchantDueDetailInfo> dueDetailInfos = new ArrayList<>();
        bMerchantCreate.setBMerchantDueDetailInfos(dueDetailInfos);
        // 数据来源
        bMerchantCreate.setDataSources(B_MERCHANT_CREATE_DATA_SOURCE_SERVICE_ENQUIRY);
        // 商家信息
        if (expressOrderContext.getCustomerConfig() != null) {
            bMerchantCreate.setSellerId(String.valueOf(expressOrderContext.getCustomerConfig().getCustomerId()));
            bMerchantCreate.setSellerName(expressOrderContext.getCustomerConfig().getCustomerName());
        }
        // 支付模式
        bMerchantCreate.setPayMode(PayModeEnum.ONLINE);
        orderBankFacadeRequest.setBMerchantCreate(bMerchantCreate);

        // 初始化暂存服务费
        BigDecimal zcAmount = BigDecimal.ZERO;
        OrderBankFacadeRequest.BMerchantDueDetailInfo dfZCDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
        dfZCDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_ZCF);
        dfZCDueDetailInfo.setAmount(zcAmount);
        dueDetailInfos.add(dfZCDueDetailInfo);
        // B商家总额赋值
        bMerchantCreate.setAmount(zcAmount);
        // 处理币种
        OrderBankCurrencyUtil.handleCurrency(expressOrderContext, orderBankFacadeRequest);

        return orderBankFacadeRequest;
    }

    /**
     * 暂存自提单台账请求对象-询价场景
     *
     * @param expressOrderContext
     * @return
     */
    public OrderBankFacadeMiddleRequest toSelfPickupTemporaryStorageEnquiryOrderBankFacadeRequest(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);
        // 获取询价后的折后总金额 当前单不存在取原单的
        BigDecimal discountAmount = GetFieldUtils.getDiscountMoney(orderModel);

        OrderBankFacadeMiddleRequest orderBankFacadeRequest = new OrderBankFacadeMiddleRequest();
        OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
        orderBankFacadeRequest.setWaybillNo(SELF_PICKUP_TEMPORARY_STORAGE_PREFIX + orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());
        orderBankFacadeRequest.setOrgId(orderModel.getFinance().getCollectionOrgNo());
        orderBankFacadeRequest.setOrgName(orderModel.getFinance().getCollectionOrgName());
        orderBankFacadeRequest.setUUid(orderModel.requestProfile().getTenantId() + "_" + orderModel.orderNo());
        orderBankFacadeRequest.setConsigneeInfo(toEnquirySceneConsigneeInfo(orderSnapshot));
        orderBankFacadeRequest.setConsignorInfo(toEnquirySceneConsignorInfo(expressOrderContext, orderSnapshot));

        if (discountAmount != null) {
            //POS到付
            posYun.setAmount(discountAmount);
            posYun.setPosType(POS_TYPE);
            posYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, orderSnapshot, orderModel.getFinance(), settlementType, true)));
            orderBankFacadeRequest.setPosYun(posYun);

            //B商家到付
            OrderBankFacadeRequest.BMerchantDueDetailInfo dfBMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            dfBMerchantDueDetailInfo.setAmount(discountAmount);
            dfBMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_ZCF);
            bMerchantDfModify.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderSnapshot));
            bMerchantDfModify.setBMerchantDueDetailInfo(dfBMerchantDueDetailInfo);
            orderBankFacadeRequest.setBMerchantDfModify(bMerchantDfModify);
        }

        // 处理币种
        OrderBankCurrencyUtil.handleCurrency(expressOrderContext, orderBankFacadeRequest);

        return orderBankFacadeRequest;
    }
}
