package cn.jdl.oms.express.shared.common.dict;

import lombok.Getter;

/**
 * 禁止使用E卡原因枚举
 */
@Getter
public enum ECardDisableReasonEnum {

    ORDER_HK_MO("1001", "港澳订单，不支持E卡"),
    ORDER_INTL("1002", "国际订单，不支持E卡"),
    DF_COD("2001", "到付订单含有代收货款，不支持E卡"),
    TICKET("2002", "订单使用了优惠券，不支持E卡"),
    ACTIVITY_GRADUATION_SEND("2003", "毕业季不支持E卡"),
    DISCOUNT_2("2004", "订单使用了揽派同时折扣，不支持E卡"),
    DISCOUNT_3("2005", "订单使用了专业市场折扣，不支持E卡"),
    DISCOUNT_10("2006", "订单使用了散单批量寄折扣，不支持E卡"),
    DISCOUNT_4("2007", "订单使用了渠道优惠，不支持E卡"),
    DISCOUNT_11("2008", "订单使用了员工福利寄优惠，不支持E卡"),
//    DISCOUNT_1("2009", "订单使用了产品折扣，不支持E卡"),
//    DISCOUNT_5("2010", "折扣：客户现结，不支持E卡"),
    DISCOUNT_6("2011", "折扣：合同客户，不支持E卡"),
    SITE_BM_JF("3001", "寄付现结且揽收站点为便民站点，不支持E卡"),
    SITE_BM_DF("3002", "到付现结且派送站点为便民站点，不支持E卡"),
    CITY_JF("3003", "寄付现结且寄件人城市，不支持E卡"),
    CITY_DF("3004", "到付现结且收件人人城市，不支持E卡"),
    REMOTE_ROUTE("3005", "偏远流向，不支持E卡"),
    SITE_THIRD_TYPE_ORIGINAL_PRICE("3006", "三方站点且原价订单，不支持E卡"),
    ECOMMERCE_RETURN("3007", "电商退货产品不支持E卡支付"),
    ;
    /**
     * 编码
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;

    ECardDisableReasonEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
