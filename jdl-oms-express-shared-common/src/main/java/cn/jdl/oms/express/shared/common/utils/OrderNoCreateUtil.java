package cn.jdl.oms.express.shared.common.utils;

import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.Map;

/**
 * @ProjectName：cn.jdl.oms.express.shared.common.utils
 * @Package： cn.jdl.oms.express.shared.common.utils
 * @ClassName: OrderNoPrefixUtil
 * @Description:订单号前缀获取工具类
 * @Author： yangxiangrong5
 * @CreateDate 2021/5/9 0:51
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
public class OrderNoCreateUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderNoCreateUtil.class);

    /**
     * 订单中心生成退款单号KEY
     */
    private static final String REFUND_ORDER_NO_SERIALNUMBER_KEY = "cn_jdl_oms_refund_order";

    /**
     * 订单中心生成改址记录号KEY
     */
    private static final String READDRESS_RECORD_NO_SERIALNUMBER_KEY = "cn_jdl_oms_readdress_record";

    /**
     * 订单号生成前缀 ( key:业务单元，value:订单前缀)
     */
    private static Map<String,String> prefixMap = new HashMap<>();

    /**
     * 订单号生成序列号key  ( key:业务单元，value:序列号key)
     */
    private static Map<String,String> serialnumberKeyMap =new HashMap<>();

    /**
     * 统一变更修改记录单号规则为：FxF前缀+运单号
     */
    public static final String MODIFY_RECORD_NO_PREFIX = "F";

    // 以下变更记录前缀为兼容历史数据判断先保留

    /**
     * 改址变更记录前缀
     */
    @Deprecated
    public static final String READDRESS_RECORD_NO_PREFIX = "G";

    /**
     * 复重量方变更记录前缀
     */
    @Deprecated
    public static final String RECHECK_RECORD_NO_PREFIX = "C";

    /**
     * 快运变更记录前缀
     */
    @Deprecated
    public static final String FREIGHT_RECORD_NO_PREFIX = "F";

    private static final String DEFAULT_PREFIX = "ECO";

    /**
    * <AUTHOR>
    * @Description 构造方法
    * @Date 1:04 2021/5/9
    * @Param [prefix]订单前缀集合
    * @return
    **/
    private OrderNoCreateUtil(Map<BusinessUnitEnum,String> prefix,Map<BusinessUnitEnum,String> serialnumberKey){
        if(!prefix.isEmpty()){
            for(Map.Entry<BusinessUnitEnum, String> entry:prefix.entrySet()){
                if(entry.getKey() == null){
                    LOGGER.error("请检查当前key:{},不是业务单元",entry.getKey());
                }
                if(entry.getKey()!=null&&StringUtils.isNotBlank(entry.getValue())){
                    prefixMap.put(entry.getKey().businessUnit(),entry.getValue());
                }
            }
        }
        if(!serialnumberKey.isEmpty()){
            for(Map.Entry<BusinessUnitEnum, String> entry:serialnumberKey.entrySet()){
                if(entry.getKey() == null){
                    LOGGER.error("请检查当前key:{},不是业务单元",entry.getKey());
                }
                if(entry.getKey()!=null&&StringUtils.isNotBlank(entry.getValue())){
                    serialnumberKeyMap.put(entry.getKey().businessUnit(),entry.getValue());
                }
            }
        }

    }

    /***
     * <AUTHOR>
     * @Description 获取订单生成序列号的key
     * @Date 15:56 2021/5/10
     * @Param  businessUnit 业务单元
     * @return
     **/
    public static String getOrderNoPrefix(String businessUnit) {
        if (StringUtils.isBlank(businessUnit)) {
            LOGGER.error("请检查业务单元信息不存在");
            return null;
        }
        String orderNoPrefix = prefixMap.getOrDefault(businessUnit, DEFAULT_PREFIX);
        if (StringUtils.isBlank(orderNoPrefix)) {
            LOGGER.error("请检查applicationConext-orderno 配置中是否配置了业务单元对应的订单前缀信息,当前业务单元：{}", businessUnit);
        }
        return orderNoPrefix;
    }

    /***
    * <AUTHOR>
    * @Description 获取订单生成序列号的key
    * @Date 15:56 2021/5/10
    * @Param businessUnit 业务单元
    * @return
    **/
    public static String getOrderNoSerialnumberKey(String businessUnit){
        if(StringUtils.isBlank(businessUnit)){
            LOGGER.error("请检查业务单元信息不存在");
            return null;
        }
        String serialnumberKey = serialnumberKeyMap.get(businessUnit);
        if(StringUtils.isBlank(serialnumberKey)){
            LOGGER.error("请检查applicationConext-orderno 配置中是否配置了业务单元对应的订单生成序列号的key信息,当前业务单元：{}",businessUnit);
        }
        return serialnumberKey;
    }

    /**
     * 获取退款单号key
     * @return
     */
    public static String getRefundOrderNoSerialnumberKey(){
        return REFUND_ORDER_NO_SERIALNUMBER_KEY;
    }

    /**
     * 获取改址记录号key
     * @return
     */
    public static String getReaddressRecordNoSerialnumberKey(){
        return READDRESS_RECORD_NO_SERIALNUMBER_KEY;
    }

    /**
     * 判断是否改址记录号
     * @param modifyNo 单号
     * @return
     */
    public static boolean isModifyRecordNo(String modifyNo){
        // modifyNo生成规则：变更类型前缀（单个大写英文字母）+ 序号 + 变更类型前缀（单个大写英文字母）+ 订单号
        // 分割规则：基于前缀分割，分割次数limit=3，即最多被分割两次，生成数组长度为3
        // 示例1：G1GEOU0028981027959，分割结果为：["", 1, "EOU0028981027959"]
        // 示例2：G1GGOU0028981027959，分割结果为：["", 1, "GOU0028981027959"]
        String preFix = modifyNo.substring(0, 1);//改址前缀
        if (!READDRESS_RECORD_NO_PREFIX.equals(preFix)
                && !RECHECK_RECORD_NO_PREFIX.equals(preFix)
                && !FREIGHT_RECORD_NO_PREFIX.equals(preFix)
                && !MODIFY_RECORD_NO_PREFIX.equals(preFix)) {
            return false;
        }
        //前缀截取
        String[] orderNos = modifyNo.split(preFix,3);
        String orderNo;
        if (orderNos.length < 3) {
            return false;
        }
        //第二位：数字，次数
        if (!StringUtils.isNumeric(orderNos[1])) {
            return false;
        }
        //第三位：订单号
        orderNo = orderNos[2];
        return !StringUtils.isEmpty(orderNo);
    }
}
